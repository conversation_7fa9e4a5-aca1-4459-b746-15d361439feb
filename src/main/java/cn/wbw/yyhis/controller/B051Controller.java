package cn.wbw.yyhis.controller;

import cn.hutool.core.collection.CollectionUtil;
import cn.wbw.yyhis.converter.B051Converter;
import cn.wbw.yyhis.model.dto.B051DTO;
import cn.wbw.yyhis.model.dto.B051UpsertDTO;
import cn.wbw.yyhis.model.entity.B023;
import cn.wbw.yyhis.model.entity.B051;
import cn.wbw.yyhis.service.B023Service;
import cn.wbw.yyhis.service.B051Service;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 入院记录管理接口
 *
 * 提供患者入院记录的完整管理功能，包括入院记录的创建、更新、删除和查询。
 * 入院记录包含患者的主诉、现病史、既往史、个人史、家族史、体格检查、
 * 辅助检查、初步诊断等详细的医疗信息，是患者住院治疗的重要文档。
 *
 * <AUTHOR>
 * @since 2024-07-30
 */
@RestController
@RequestMapping("/b051")
@RequiredArgsConstructor
public class B051Controller {

    private final B051Service b051Service;
    private final B023Service b023Service;
    private final B051Converter converter = B051Converter.INSTANCE;

    /**
     * 保存或更新入院记录
     *
     * 创建新的入院记录或更新已存在的入院记录。系统会根据是否存在记录流水号
     * 来判断是新增还是更新操作。入院记录包含患者的完整医疗信息，
     * 是住院治疗过程中的核心文档。
     *
     * @param dto 入院记录数据传输对象，包含以下主要字段：
     *            - recordSn: 入院记录流水号（更新时必填，新增时可为空）
     *            - visitSn: 单次就诊唯一标识号（必填）
     *            - chiefComplaint: 主诉，患者主要症状和就诊原因
     *            - currentMedhistory: 现病史，当前疾病的发展过程
     *            - pastMedhistory: 既往史，患者以往的疾病史
     *            - personalMedhistory: 个人史，生活习惯、职业等信息
     *            - menstrualHistory: 月经史（女性患者）
     *            - marriageBirthHistory: 婚育史
     *            - familyHistory: 家族史，家族遗传疾病史
     *            - auxiliaryExam: 辅助检查结果
     *            - physicalExam: 体格检查结果
     *            - primaryDiagnosis: 初步诊断
     * @return B051DTO 保存成功的入院记录完整信息，包含系统生成的记录流水号等字段
     */
    @PostMapping("/save")
    public B051DTO save(@RequestBody B051UpsertDTO dto) {
        B051 savedB051 = b051Service.upsertAdmissionRecord(dto);
        return converter.entityToDto(savedB051);
    }

    /**
     * 删除入院记录
     *
     * 根据就诊流水号删除指定患者的入院记录。
     * 删除操作会彻底移除该患者的入院记录信息，请谨慎操作。
     * 建议在删除前确认该记录不再需要或已有备份。
     *
     * @param visitSn 单次就诊唯一标识号，用于定位要删除的入院记录
     * @return Boolean 删除操作结果，true表示删除成功，false表示删除失败
     */
    @DeleteMapping("/delete/{visitSn}")
    public Boolean delete(@PathVariable String visitSn) {
        return  b051Service.deleteByVisitSn(visitSn);
    }

    /**
     * 根据就诊流水号查询入院记录详情
     *
     * 查询指定患者的入院记录详细信息，包含患者的主诉、病史、
     * 体格检查、辅助检查等完整的入院记录内容。
     * 系统会自动关联查询该患者的入院诊断信息，并将多个诊断名称
     * 合并显示在初步诊断字段中。
     *
     * @param visitSn 单次就诊唯一标识号，用于定位入院记录
     * @return B051DTO 入院记录详细信息对象，包含：
     *         - 基本信息：记录流水号、就诊流水号、患者ID等
     *         - 病史信息：主诉、现病史、既往史、个人史、家族史等
     *         - 检查信息：体格检查、辅助检查结果等
     *         - 诊断信息：初步诊断（自动关联入院诊断记录）
     *         - 生命体征：体温、心率、血压、呼吸等
     *         - 评分信息：KPS评分、ECOG评分等
     *         - 系统字段：记录时间、更新时间、数据状态等
     *         如果未找到对应记录，返回null
     */
    @GetMapping("/detail/{visitSn}")
    public B051DTO findById(@PathVariable String visitSn) {
        B051 one = b051Service.lambdaQuery().eq(B051::getVisitSn, visitSn).one();
        if (one != null) {
            List<B023> list = b023Service.lambdaQuery()
                    .eq(B023::getVisitSn, visitSn)
                    .eq(B023::getDiagType, "入院诊断").select(B023::getDiagName).list();
            String primaryDiagnosis ="";
            if (CollectionUtil.isNotEmpty(list)) {
                primaryDiagnosis = list.stream().map(B023::getDiagName).collect(Collectors.joining("，"));
            }
            one.setPrimaryDiagnosis(primaryDiagnosis);
        }
        return converter.entityToDto(one);
    }

}