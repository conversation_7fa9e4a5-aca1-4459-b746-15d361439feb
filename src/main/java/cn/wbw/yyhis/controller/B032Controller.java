package cn.wbw.yyhis.controller;

import cn.wbw.yyhis.converter.B032Converter;
import cn.wbw.yyhis.model.dto.B032DTO;
import cn.wbw.yyhis.model.dto.DepartmentPatientCountDTO;
import cn.wbw.yyhis.model.entity.B032;
import cn.wbw.yyhis.model.vo.PatientCardVO;
import cn.wbw.yyhis.service.B032Service;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 住院患者信息管理接口
 *
 * 提供在院患者信息的完整管理功能，包括患者基本信息维护、
 * 患者卡片查询、科室患者统计等功能。主要用于住院患者的
 * 日常管理和统计分析。
 *
 * <AUTHOR>
 * @since 2024-07-30
 */
@RestController
@RequestMapping("/b032")
@RequiredArgsConstructor
public class B032Controller {

    private final B032Service b032Service;
    private final B032Converter b032Converter = B032Converter.INSTANCE;

    /**
     * 添加住院患者信息
     *
     * 创建新的住院患者记录，包括患者基本信息、住院信息、
     * 责任医生、责任护士等完整的在院患者数据。
     *
     * @param b032DTO 住院患者信息数据传输对象，包含以下主要字段：
     *                - visitSn: 单次就诊唯一标识号（必填）
     *                - patientId: 患者ID（必填）
     *                - name: 患者姓名
     *                - inpatientNo: 住院号
     *                - medicalRecordNo: 病案号
     *                - visitDoctorName: 就诊医生姓名
     *                - visitDoctorNo: 就诊医生代码
     *                - responsibleNurse: 责任护士
     *                - hospitalizationTimes: 住院次数
     * @return Boolean 添加操作结果，true表示添加成功，false表示添加失败
     */
    @PostMapping("/add")
    public Boolean add(@RequestBody B032DTO b032DTO) {
        B032 b032 = b032Converter.dtoToEntity(b032DTO);
        return b032Service.save(b032);
    }

    /**
     * 删除住院患者信息
     *
     * 根据就诊流水号删除指定的住院患者记录。
     * 删除操作会彻底移除该患者的在院信息，请谨慎操作。
     *
     * @param id 就诊流水号，用于唯一标识一次住院记录
     * @return Boolean 删除操作结果，true表示删除成功，false表示删除失败
     */
    @DeleteMapping("/delete/{id}")
    public Boolean delete(@PathVariable("id") String id) {
        return b032Service.removeById(id);
    }

    /**
     * 更新住院患者信息
     *
     * 根据就诊流水号更新指定患者的住院信息，可以更新患者基本信息、
     * 责任医生、责任护士、住院状态等字段。
     *
     * @param id 就诊流水号，用于定位要更新的患者记录
     * @param b032DTO 更新数据传输对象，包含要更新的字段信息
     * @return Boolean 更新操作结果，true表示更新成功，false表示更新失败
     */
    @PutMapping("/update/{id}")
    public Boolean update(@PathVariable("id") String id, @RequestBody B032DTO b032DTO) {
        b032DTO.setVisitSn(id);
        B032 b032 = b032Converter.dtoToEntity(b032DTO);
        return b032Service.updateById(b032);
    }

    /**
     * 查询所有住院患者信息
     *
     * 获取系统中所有在院患者的完整信息列表，
     * 适用于患者信息的全量查询和导出功能。
     *
     * @return List<B032DTO> 所有住院患者信息列表，包含患者基本信息、
     *         住院信息、医护人员信息等完整数据
     */
    @GetMapping("/all")
    public List<B032DTO> findAll() {
        List<B032> list = b032Service.list();
        return b032Converter.entityListToDtoList(list);
    }

    /**
     * 根据就诊流水号获取患者卡片信息
     *
     * 查询指定患者的卡片展示信息，包括患者基本信息、
     * 入院诊断、床位信息、住院天数等关键信息，
     * 适用于患者卡片的详情展示。
     *
     * @param visitSn 单次就诊唯一标识号
     * @return PatientCardVO 患者卡片信息对象，包含：
     *         - medicalRecordNo: 病历号
     *         - patientName: 患者姓名
     *         - bedNo: 床号
     *         - attendingPhysician: 床位医生
     *         - admissionDiagnosis: 入院诊断
     *         - patientAge: 患者年龄
     *         - admissionTime: 入院时间
     *         - hospitalizationDays: 住院天数
     *         - gender: 性别
     *         - ethnicity: 民族
     */
    @GetMapping("/patient-cards/getByVisitSn")
    public PatientCardVO getPatientCard(@RequestParam("visitSn") String visitSn) {
        return b032Service.getPatientCard(visitSn);
    }

    /**
     * 根据科室代码获取患者卡片列表
     *
     * 查询指定科室的所有在院患者卡片信息，
     * 支持按数据来源进行筛选，适用于科室患者管理界面。
     *
     * @param departmentCode 科室代码，用于筛选特定科室的患者
     * @param source 数据来源，用于区分不同来源的患者数据
     * @return List<PatientCardVO> 患者卡片信息列表，按科室分组展示
     */
    @GetMapping("/patient-cards")
    public List<PatientCardVO> getPatientCards(@RequestParam("departmentCode") String departmentCode,@RequestParam String source) {
        return b032Service.getPatientCards(departmentCode,source);
    }

    /**
     * 按科室统计患者数量
     *
     * 统计各科室的在院患者数量，用于生成科室患者分布报表，
     * 支持按数据来源进行统计分析。
     *
     * @param source 数据来源，用于区分不同来源的统计数据
     * @return List<DepartmentPatientCountDTO> 科室患者统计列表，包含：
     *         - departmentCode: 科室代码
     *         - departmentName: 科室名称
     *         - patientCount: 患者数量
     *         - 其他统计相关字段
     */
    @GetMapping("/stats/by-department")
    public List<DepartmentPatientCountDTO> countPatientsByDepartment(@RequestParam String source ) {
        return b032Service.countPatientsByDepartment(source);
    }

    /**
     * 分页查询住院患者信息
     *
     * 分页获取住院患者信息，支持自定义页码和每页数量，
     * 适用于患者信息的列表展示和管理功能。
     *
     * @param pageNum 页码，默认值为1，表示第一页
     * @param pageSize 每页记录数，默认值为10
     * @return Page<B032DTO> 分页结果对象，包含：
     *         - records: 当前页的患者信息列表
     *         - current: 当前页码
     *         - size: 每页大小
     *         - total: 总记录数
     *         - pages: 总页数
     */
    @GetMapping("/page")
    public Page<B032DTO> findPage(@RequestParam(defaultValue = "1") Integer pageNum,
                                  @RequestParam(defaultValue = "10") Integer pageSize) {
        Page<B032> page = new Page<>(pageNum, pageSize);
        Page<B032> b032Page = b032Service.page(page);
        Page<B032DTO> dtoPage = new Page<>(b032Page.getCurrent(), b032Page.getSize(), b032Page.getTotal());
        dtoPage.setRecords(b032Converter.entityListToDtoList(b032Page.getRecords()));
        return dtoPage;
    }
}