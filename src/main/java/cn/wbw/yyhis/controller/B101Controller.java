package cn.wbw.yyhis.controller;

import cn.wbw.yyhis.common.ApiResult;
import cn.wbw.yyhis.converter.B101Converter;
import cn.wbw.yyhis.exception.BusinessException;
import cn.wbw.yyhis.model.dto.B101DTO;
import cn.wbw.yyhis.model.dto.B101UpsertDTO;
import cn.wbw.yyhis.model.entity.B101;
import cn.wbw.yyhis.service.B101Service;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 住院医嘱记录管理接口
 *
 * 提供住院患者医嘱记录的完整管理功能，包括医嘱的创建、更新、删除和查询。
 * 医嘱记录是医生对患者治疗方案的具体指示，包括药物医嘱、检查医嘱、
 * 护理医嘱等多种类型，是医疗质量管理和患者安全的重要保障。
 *
 * <AUTHOR>
 * @since 2024-07-30
 */
@RestController
@RequestMapping("/b101")
@RequiredArgsConstructor
public class B101Controller {

    private final B101Service b101Service;
    private final B101Converter converter = B101Converter.INSTANCE;

    /**
     * 新增住院医嘱记录
     *
     * 创建新的医嘱记录，包括药物医嘱、检查医嘱、护理医嘱等。
     * 医嘱是医生对患者治疗的具体指示，包含用药方案、检查项目、
     * 护理措施等详细信息，是医疗执行的重要依据。
     *
     * @param dto 医嘱记录数据传输对象，包含以下主要字段：
     *            - visitSn: 单次就诊唯一标识号（必填）
     *            - orderType: 医嘱类型（如：药物医嘱、检查医嘱等）
     *            - orderClassCode: 医嘱类别代码
     *            - orderClassName: 医嘱类别名称
     *            - orderItemName: 医嘱名称（必填）
     *            - spec: 药品规格（药物医嘱时填写）
     *            - frequencyCode: 频次代码（如：bid、tid等）
     *            - frequencyName: 频次名称（如：每日两次、每日三次等）
     *            - administrationRoute: 用法/给药途径（如：口服、静脉注射等）
     *            - dose: 用量
     *            - doseUnit: 用量单位
     *            - orderStartDatetime: 医嘱开始时间
     *            - orderEndDatetime: 医嘱结束时间
     * @return ApiResult 统一返回结果，包含操作状态和创建的医嘱记录信息
     *         成功时返回完整的医嘱记录信息，失败时返回错误信息
     */
    @PostMapping("/add")
    public ApiResult add(@RequestBody B101UpsertDTO dto) {
        try {
            B101 newB101 = b101Service.addMedicalOrder(dto);
            B101DTO b101DTO = converter.entityToDto(newB101);
            return ApiResult.success(b101DTO);
        } catch (BusinessException e) {
            return ApiResult.error(1002,e.getMessage());
        }
    }

    /**
     * 根据医嘱流水号删除住院医嘱记录
     *
     * 删除指定的医嘱记录。删除医嘱需要谨慎操作，
     * 建议在删除前确认该医嘱已停止执行或已有替代方案。
     *
     * @param orderSn 医嘱流水号，用于唯一标识一条医嘱记录
     * @return Boolean 删除操作结果，true表示删除成功，false表示删除失败
     */
    @DeleteMapping("/delete/{orderSn}")
    public Boolean delete(@PathVariable String orderSn) {
      return   b101Service.delete(orderSn);
    }

    /**
     * 更新住院医嘱记录
     *
     * 根据医嘱流水号更新指定的医嘱记录信息。
     * 可以更新医嘱的执行频次、用量、给药途径、执行时间等信息。
     * 医嘱更新需要严格的权限控制，确保医疗安全。
     *
     * @param orderSn 医嘱流水号，用于定位要更新的医嘱记录
     * @param dto 更新数据传输对象，包含要更新的医嘱信息
     * @return ApiResult 统一返回结果，包含操作状态
     *         成功时返回更新结果，失败时返回错误信息
     */
    @PutMapping("/update/{orderSn}")
    public ApiResult update(@PathVariable String orderSn, @RequestBody B101UpsertDTO dto) {
        try {
            dto.setOrderSn(orderSn);
            boolean b = b101Service.updateMedicalOrder(dto);
            return ApiResult.success(b);
        } catch (BusinessException e) {
            return ApiResult.error(1002,e.getMessage());
        }
    }

    /**
     * 根据就诊流水号查询住院医嘱记录列表
     *
     * 查询指定患者就诊期间的所有医嘱记录，按记录创建时间倒序排列，
     * 最新的医嘱记录排在前面。适用于查看患者的完整医嘱历史。
     *
     * @param visitSn 单次就诊唯一标识号，用于筛选特定就诊的医嘱记录
     * @return List<B101DTO> 医嘱记录列表，包含该就诊的所有医嘱记录，
     *         每条记录包含医嘱内容、执行信息、开立医生等完整信息：
     *         - orderItemName: 医嘱名称
     *         - orderType: 医嘱类型
     *         - dose: 用量
     *         - frequencyName: 频次
     *         - administrationRoute: 给药途径
     *         - orderStartDatetime: 开始时间
     *         - orderEndDatetime: 结束时间
     *         - orderDoctorName: 开立医生
     *         - orderStatus: 医嘱状态
     *         - 其他相关字段
     *         如果该就诊没有医嘱记录，返回空列表
     */
    @GetMapping("/list/{visitSn}")
    public List<B101DTO> listByVisitSn(@PathVariable String visitSn) {
        List<B101> list = b101Service.lambdaQuery().eq(B101::getVisitSn, visitSn)
                .orderByDesc(B101::getRecordDatetime)
                .list();
        return converter.entityListToDtoList(list);
    }

    /**
     * 根据医嘱流水号查询住院医嘱记录详情
     *
     * 查询指定医嘱记录的详细信息，包含医嘱的所有字段信息，
     * 适用于医嘱的详情查看和执行确认。
     *
     * @param orderSn 医嘱流水号，用于定位具体的医嘱记录
     * @return B101DTO 医嘱记录详细信息对象，包含：
     *         - 基本信息：医嘱名称、类型、状态等
     *         - 用药信息：药品规格、用量、频次、给药途径等
     *         - 时间信息：开始时间、结束时间、确认时间等
     *         - 医生信息：开立医生、确认医生等
     *         - 执行信息：执行科室、执行状态等
     *         - 费用信息：单价、总价等
     *         - 系统字段：记录时间、更新时间等
     *         如果未找到对应记录，返回null
     */
    @GetMapping("/detail/{orderSn}")
    public B101DTO findById(@PathVariable String orderSn) {
        return converter.entityToDto(b101Service.getById(orderSn));
    }

    /**
     * 分页查询住院医嘱记录
     *
     * 分页获取医嘱记录信息，支持自定义页码和每页数量，
     * 适用于医嘱记录的列表展示和管理功能。
     *
     * @param pageNum 页码，默认值为1，表示第一页
     * @param pageSize 每页记录数，默认值为10
     * @return Page<B101DTO> 分页结果对象，包含：
     *         - records: 当前页的医嘱记录列表
     *         - current: 当前页码
     *         - size: 每页大小
     *         - total: 总记录数
     *         - pages: 总页数
     */
    @GetMapping("/page")
    public Page<B101DTO> findPage(@RequestParam(defaultValue = "1") Integer pageNum,
                                  @RequestParam(defaultValue = "10") Integer pageSize) {
        Page<B101> page = new Page<>(pageNum, pageSize);
        Page<B101> entityPage = b101Service.page(page);
        Page<B101DTO> dtoPage = new Page<>(entityPage.getCurrent(), entityPage.getSize(), entityPage.getTotal());
        dtoPage.setRecords(converter.entityListToDtoList(entityPage.getRecords()));
        return dtoPage;
    }
}