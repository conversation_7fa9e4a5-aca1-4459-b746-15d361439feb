package cn.wbw.yyhis.controller;

import org.springframework.web.bind.annotation.*;
import org.springframework.beans.factory.annotation.Autowired;
import cn.wbw.yyhis.model.dto.B163DTO;
import cn.wbw.yyhis.model.entity.B163;
import cn.wbw.yyhis.service.B163Service;
import cn.wbw.yyhis.converter.B163Converter;

import java.util.List;

/**
 * 分子病理检测记录管理接口
 *
 * 提供患者分子病理检测记录的管理功能，包括基因检测、分子标志物检测等
 * 精准医学相关的检测项目记录管理。分子病理检测是精准医学的重要组成部分，
 * 为个体化治疗提供分子层面的诊断依据，指导靶向治疗和免疫治疗的选择。
 *
 * <AUTHOR>
 * @since 2024-07-26
 */
@RestController
@RequestMapping("/b163")
public class B163Controller {

    @Autowired
    private B163Service b163Service;

    @Autowired
    private B163Converter b163Converter;

    /**
     * 更新分子病理检测记录
     *
     * 更新指定的分子病理检测记录信息，可以修改检测方法、检测结果等字段。
     * 分子病理检测记录的更新需要严格的权限控制，确保检测结果的准确性，
     * 因为这些结果直接影响精准治疗方案的制定。
     *
     * @param b163DTO 分子病理检测记录数据传输对象，包含以下主要字段：
     *                - reportNo: 报告单号，分子病理检测记录的唯一标识
     *                - reportDatetime: 报告时间
     *                - applyDatetime: 申请时间
     *                - testMethod: 检测方法（如：PCR、测序、免疫组化等）
     *                - testResult: 检测结果，包含基因突变、表达水平等信息
     * @return Boolean 更新操作结果，true表示更新成功，false表示更新失败
     */
    @PutMapping("/update")
    public Boolean update(@RequestBody B163DTO b163DTO) {
        return b163Service.updateDto(b163DTO);
    }

    /**
     * 根据报告单号删除分子病理检测记录
     *
     * 删除指定的分子病理检测记录。删除分子病理检测记录需要谨慎操作，
     * 因为这些检测结果是精准医学治疗的重要依据，
     * 建议在删除前确认该记录不再需要或已有备份。
     *
     * @param id 报告单号，用于唯一标识一条分子病理检测记录
     * @return Boolean 删除操作结果，true表示删除成功，false表示删除失败
     */
    @DeleteMapping("/delete/{id}")
    public Boolean delete(@PathVariable("id") String id) {
        return b163Service.deleteByReportNo(id);
    }

    /**
     * 根据就诊流水号查询分子病理检测记录列表
     *
     * 查询指定患者就诊期间的所有分子病理检测记录，包括基因检测、
     * 分子标志物检测等精准医学相关检测。这些检测结果为医生提供
     * 分子层面的诊断信息，指导个体化治疗方案的制定。
     *
     * @param visitSn 单次就诊唯一标识号，用于筛选特定就诊的分子病理检测记录
     * @return List<B163DTO> 分子病理检测记录列表，包含该就诊的所有分子病理检测记录：
     *         - reportNo: 报告单号
     *         - reportDatetime: 报告时间
     *         - applyDatetime: 申请时间
     *         - testMethod: 检测方法
     *         - testResult: 检测结果
     *         如果该就诊没有分子病理检测记录，返回空列表
     */
    @GetMapping("/list/{visitSn}")
    public List<B163DTO> list(@PathVariable String visitSn) {
        List<B163> list = b163Service.lambdaQuery().eq(B163::getVisitSn, visitSn).list();
        return list.stream().map(b163Converter::entityToDto).toList();
    }
}