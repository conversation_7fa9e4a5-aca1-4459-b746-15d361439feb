package cn.wbw.yyhis.controller;

import cn.wbw.yyhis.converter.B0612Converter;
import cn.wbw.yyhis.model.dto.B0612DTO;
import cn.wbw.yyhis.model.dto.B0612UpsertDTO;
import cn.wbw.yyhis.model.entity.B0612;
import cn.wbw.yyhis.service.B0612Service;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.RequiredArgsConstructor;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 会诊记录管理接口
 *
 * 提供患者会诊记录的管理功能，包括多学科会诊、专科会诊等医疗会诊活动的记录管理。
 * 会诊记录是医疗机构内部或跨机构专家协作诊疗的重要文档，
 * 记录了会诊申请、会诊过程、专家意见等关键信息，为复杂疾病的诊疗提供专业支持。
 *
 * <AUTHOR>
 * @since 2024-07-30
 */
@RestController
@RequestMapping("/b0612")
@RequiredArgsConstructor
public class B0612Controller {

    private final B0612Service b0612Service;
    private final B0612Converter converter = B0612Converter.INSTANCE;

    /**
     * 保存或更新会诊记录
     *
     * 根据是否提供记录流水号来判断是新增还是更新操作。
     * 会诊记录是多学科协作诊疗的重要文档，记录了会诊申请、
     * 会诊过程、专家意见等关键信息。
     *
     * @param dto 会诊记录数据传输对象，包含以下主要字段：
     *            - recordSn: 病程记录流水号（更新时必填，新增时可为空）
     *            - visitSn: 单次就诊唯一标识号（必填）
     *            - consApplyTime: 会诊申请日期
     *            - consTime: 会诊日期，实际进行会诊的时间
     *            - diagnosisConsultationCorrected: 诊断名称，会诊确认的诊断
     *            - auxiliaryExam: 辅助检查，相关的检查检验结果
     *            - recordAbstract: 患者病情，病情摘要描述
     *            - treatProDescription: 诊疗情况，已采取的治疗措施
     *            - consReason: 会诊原因及目的，申请会诊的理由
     *            - deptInvited: 邀请科室，参与会诊的科室
     *            - consRecommendation: 会诊意见，专家给出的诊疗建议
     * @return B0612DTO 保存后的会诊记录完整信息
     */
    @PostMapping("/save")
    public B0612DTO save(@RequestBody B0612UpsertDTO dto) {
        if (!StringUtils.hasText(dto.getRecordSn())) {
            B0612 newB0612 = b0612Service.addConsultationRecord(dto);
            return converter.entityToDto(newB0612);
        } else {
            b0612Service.updateConsultationRecord(dto);
            return converter.entityToDto(b0612Service.getById(dto.getRecordSn()));
        }
    }

    /**
     * 根据就诊流水号删除会诊记录
     *
     * 删除指定就诊的会诊记录。删除会诊记录需要谨慎操作，
     * 因为会诊记录是重要的医疗协作文档，记录了专家的诊疗意见，
     * 建议在删除前确认该记录确实需要删除且已有备份。
     *
     * @param visitSn 单次就诊唯一标识号
     * @return Boolean 删除操作结果，true表示删除成功，false表示删除失败
     */
    @DeleteMapping("/delete/{visitSn}")
    public Boolean delete(@PathVariable String visitSn) {
        return b0612Service.deleteByVisitSn(visitSn);
    }

    /**
     * 查询所有会诊记录
     *
     * 获取系统中的所有会诊记录，用于统计分析或管理查看。
     * 该接口返回完整的会诊记录列表，包含所有患者的会诊信息。
     *
     * @return List<B0612DTO> 所有会诊记录列表，包含：
     *         - recordSn: 病程记录流水号
     *         - visitSn: 就诊流水号
     *         - consApplyTime: 会诊申请时间
     *         - consTime: 会诊时间
     *         - diagnosisConsultationCorrected: 会诊诊断
     *         - consReason: 会诊原因
     *         - deptInvited: 邀请科室
     *         - consRecommendation: 会诊意见
     *         - recordDatetime: 记录创建时间
     */
    @GetMapping("/all")
    public List<B0612DTO> findAll() {
        return converter.entityListToDtoList(b0612Service.list());
    }

    /**
     * 根据就诊流水号查询会诊记录详情
     *
     * 查询指定就诊的会诊记录详细信息，包含完整的会诊过程记录。
     *
     * @param visitSn 单次就诊唯一标识号
     * @return B0612DTO 会诊记录详细信息，包含：
     *         - recordSn: 病程记录流水号
     *         - visitSn: 就诊流水号
     *         - consApplyTime: 会诊申请时间
     *         - consTime: 会诊时间
     *         - diagnosisConsultationCorrected: 会诊确认诊断
     *         - auxiliaryExam: 辅助检查结果
     *         - recordAbstract: 患者病情摘要
     *         - treatProDescription: 诊疗情况描述
     *         - consReason: 会诊原因及目的
     *         - deptInvited: 邀请参与的科室
     *         - consRecommendation: 专家会诊意见
     *         - signatureDoctor: 签名医生
     *         - recordDatetime: 记录创建时间
     *         如果该就诊没有会诊记录，返回null
     */
    @GetMapping("/detail/{visitSn}")
    public B0612DTO detail(@PathVariable String visitSn) {
        return converter.entityToDto(b0612Service.lambdaQuery().eq(B0612::getVisitSn, visitSn).one());
    }

    /**
     * 分页查询会诊记录
     *
     * 分页获取会诊记录列表，支持大数据量的会诊记录查询和展示。
     * 适用于会诊记录的管理界面和统计分析。
     *
     * @param pageNum 页码，从1开始，默认值为1
     * @param pageSize 每页记录数，默认值为10
     * @return Page<B0612DTO> 分页的会诊记录列表，包含：
     *         - current: 当前页码
     *         - size: 每页大小
     *         - total: 总记录数
     *         - records: 当前页的会诊记录列表，每条记录包含完整的会诊信息
     */
    @GetMapping("/page")
    public Page<B0612DTO> findPage(@RequestParam(defaultValue = "1") Integer pageNum,
                                   @RequestParam(defaultValue = "10") Integer pageSize) {
        Page<B0612> page = new Page<>(pageNum, pageSize);
        Page<B0612> entityPage = b0612Service.page(page);
        Page<B0612DTO> dtoPage = new Page<>(entityPage.getCurrent(), entityPage.getSize(), entityPage.getTotal());
        dtoPage.setRecords(converter.entityListToDtoList(entityPage.getRecords()));
        return dtoPage;
    }
} 