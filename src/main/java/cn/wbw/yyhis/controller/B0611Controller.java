package cn.wbw.yyhis.controller;

import cn.hutool.core.collection.CollectionUtil;
import cn.wbw.yyhis.converter.B0611Converter;
import cn.wbw.yyhis.model.dto.B0611DTO;
import cn.wbw.yyhis.model.dto.B0611UpsertDTO;
import cn.wbw.yyhis.model.entity.B023;
import cn.wbw.yyhis.model.entity.B0611;
import cn.wbw.yyhis.service.B023Service;
import cn.wbw.yyhis.service.B0611Service;
import lombok.RequiredArgsConstructor;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 首次病程记录管理接口
 *
 * 提供患者首次病程记录的管理功能，包括入院后首次病程记录的创建、更新、删除和查询。
 * 首次病程记录是患者入院后医生书写的第一份病程记录，详细记录患者的入院诊断、
 * 病历特点、诊疗计划等重要医疗信息，是住院治疗的重要医疗文档。
 *
 * <AUTHOR>
 * @since 2024-07-30
 */
@RestController
@RequestMapping("/b0611")
@RequiredArgsConstructor
public class B0611Controller {

    private final B0611Service b0611Service;
    private final B023Service b023Service;
    private final B0611Converter converter = B0611Converter.INSTANCE;

    /**
     * 保存或更新首次病程记录
     *
     * 根据是否提供记录流水号来判断是新增还是更新操作。
     * 首次病程记录是患者入院后的重要医疗文档，包含详细的诊疗信息。
     * 新增时会自动生成记录流水号，更新时需要提供现有的记录流水号。
     *
     * @param dto 首次病程记录数据传输对象，包含以下主要字段：
     *            - recordSn: 病程记录流水号（更新时必填，新增时可为空）
     *            - visitSn: 单次就诊唯一标识号（必填）
     *            - primaryDiagnosis: 入院初步诊断
     *            - caseCharacter: 病历特点，患者病情的特殊性描述
     *            - chiefComplaint: 主诉，患者主要症状和就诊原因
     *            - physicalExam: 体格检查结果
     *            - auxiliaryExam: 辅助检查结果（如影像、检验等）
     *            - diagnosisBasis: 诊断依据，支持诊断的客观证据
     *            - differentiatedDiagnosisDesc: 鉴别诊断信息
     *            - treatmentPlan: 诊疗计划，后续治疗方案
     * @return B0611DTO 保存后的首次病程记录完整信息
     */
    @PostMapping("/save")
    public B0611DTO save(@RequestBody B0611UpsertDTO dto) {
        if (!StringUtils.hasText(dto.getRecordSn())) {
            B0611 newB0611 = b0611Service.addFirstCourseRecord(dto);
            return converter.entityToDto(newB0611);
        } else {
            b0611Service.updateFirstCourseRecord(dto);
            return converter.entityToDto(b0611Service.getById(dto.getRecordSn()));
        }
    }

    /**
     * 根据就诊流水号删除首次病程记录
     *
     * 删除指定就诊的首次病程记录。删除病程记录需要谨慎操作，
     * 因为病程记录是重要的医疗文档，建议在删除前确认该记录
     * 确实需要删除且已有备份。
     *
     * @param visitSn 单次就诊唯一标识号
     * @return Boolean 删除操作结果，true表示删除成功，false表示删除失败
     */
    @DeleteMapping("/delete/{visitSn}")
    public Boolean delete(@PathVariable String visitSn) {
       return b0611Service.deleteByVisitSn(visitSn);
    }

    /**
     * 根据就诊流水号查询首次病程记录详情
     *
     * 查询指定就诊的首次病程记录详细信息，并自动关联入院诊断信息。
     * 该接口会自动从诊断记录表中获取该就诊的入院诊断，
     * 并将诊断名称组合后填充到主要诊断字段中。
     *
     * @param visitSn 单次就诊唯一标识号
     * @return B0611DTO 首次病程记录详细信息，包含：
     *         - recordSn: 病程记录流水号
     *         - visitSn: 就诊流水号
     *         - primaryDiagnosis: 入院初步诊断（自动从诊断表关联获取）
     *         - caseCharacter: 病历特点
     *         - chiefComplaint: 主诉
     *         - physicalExam: 体格检查
     *         - auxiliaryExam: 辅助检查
     *         - diagnosisBasis: 诊断依据
     *         - differentiatedDiagnosisDesc: 鉴别诊断
     *         - treatmentPlan: 诊疗计划
     *         - recordDatetime: 记录创建时间
     *         - signatureDoctor: 签名医生
     *         如果该就诊没有首次病程记录，返回null
     */
    @GetMapping("/detail/{visitSn}")
    public B0611DTO detail(@PathVariable String visitSn) {
        B0611 one = b0611Service.lambdaQuery().eq(B0611::getVisitSn, visitSn).one();
        if (one != null) {
            List<B023> list = b023Service.lambdaQuery()
                    .eq(B023::getVisitSn, visitSn)
                    .eq(B023::getDiagType, "入院诊断").select(B023::getDiagName).list();
            String primaryDiagnosis ="";
            if (CollectionUtil.isNotEmpty(list)) {
                primaryDiagnosis = list.stream().map(B023::getDiagName).collect(Collectors.joining("，"));
            }
            one.setPrimaryDiagnosis(primaryDiagnosis);
        }
        return converter.entityToDto(one);
    }


} 