package cn.wbw.yyhis.controller;

import cn.hutool.core.util.StrUtil;
import cn.wbw.yyhis.converter.SystemDictDataConverter;
import cn.wbw.yyhis.model.dto.SystemDictDataDTO;
import cn.wbw.yyhis.model.entity.SystemDictData;
import cn.wbw.yyhis.service.SystemDictDataService;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;
import java.util.List;

/**
 * 系统字典数据管理接口
 *
 * 提供系统字典数据的管理功能，包括字典数据的增删改查、导入等操作。
 * 字典数据是系统的基础配置信息，为医疗信息系统提供标准化的数据选项，
 * 如疾病编码（ICD-10）、药品分类、检查项目等，确保数据的标准化和一致性。
 *
 * <AUTHOR>
 * @since 2024-07-30
 */
@RestController
@RequestMapping("/system-dict-data")
@RequiredArgsConstructor
public class SystemDictDataController {

    private final SystemDictDataService systemDictDataService;
    private final SystemDictDataConverter converter = SystemDictDataConverter.INSTANCE;

    @PostMapping("/import")
    public Boolean importData() throws IOException {
        systemDictDataService.importIcd10Data();
        return true;
    }

    @PostMapping("/add")
    public Boolean add(@RequestBody SystemDictDataDTO dto) {
        return systemDictDataService.save(converter.dtoToEntity(dto));
    }

    @DeleteMapping("/delete/{id}")
    public Boolean delete(@PathVariable Long id) {
        return systemDictDataService.removeById(id);
    }

    @PutMapping("/update/{id}")
    public Boolean update(@PathVariable Long id, @RequestBody SystemDictDataDTO dto) {
        dto.setId(id);
        return systemDictDataService.updateById(converter.dtoToEntity(dto));
    }

    @GetMapping("/all")
    public List<SystemDictDataDTO> findAll() {
        return converter.entityListToDtoList(systemDictDataService.list());
    }

    @GetMapping("/detail/{id}")
    public SystemDictDataDTO findById(@PathVariable Long id) {
        return converter.entityToDto(systemDictDataService.getById(id));
    }

    @GetMapping("/page")
    public Page<SystemDictDataDTO> findPage(@RequestParam(defaultValue = "1") Integer pageNum,
                                            @RequestParam(defaultValue = "10") Integer pageSize,
                                            @RequestParam String  dictType,
                                            @RequestParam(required = false) String label) {
        Page<SystemDictData> page = new Page<>(pageNum, pageSize);
        LambdaQueryWrapper<SystemDictData> wrapper = new QueryWrapper<SystemDictData>().lambda()
                .eq(SystemDictData::getDictType, dictType)
                .like(StrUtil.isNotBlank(label),SystemDictData::getLabel, label);
        Page<SystemDictData> entityPage = systemDictDataService.page(page,wrapper);
        Page<SystemDictDataDTO> dtoPage = new Page<>(entityPage.getCurrent(), entityPage.getSize(), entityPage.getTotal());
        dtoPage.setRecords(converter.entityListToDtoList(entityPage.getRecords()));
        return dtoPage;
    }

    @GetMapping("/{dictType}")
    public List<SystemDictDataDTO> getByDictType(@PathVariable String dictType) {
        List<SystemDictData> list = systemDictDataService.lambdaQuery().eq(SystemDictData::getDictType, dictType).list();
        return converter.entityListToDtoList(list);
    }
} 