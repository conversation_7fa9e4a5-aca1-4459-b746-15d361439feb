package cn.wbw.yyhis.controller;

import cn.wbw.yyhis.converter.B171Converter;
import cn.wbw.yyhis.model.dto.B171DTO;
import cn.wbw.yyhis.model.entity.B171;
import cn.wbw.yyhis.service.B171Service;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 常规检验记录（主）管理接口
 *
 * 提供患者常规检验记录主表的查询功能，包括血液检验、尿液检验、生化检验等
 * 各类实验室检验项目的主要信息管理。常规检验是临床诊断和治疗监测的重要依据，
 * 为医生提供患者生理生化指标的客观数据支持。
 *
 * <AUTHOR>
 * @since 2024-07-26
 */
@RestController
@RequestMapping("/b171")
@RequiredArgsConstructor
public class B171Controller {

    private final B171Service b171Service;
    private final B171Converter b171Converter = B171Converter.INSTANCE;

    /**
     * 根据就诊流水号查询常规检验记录（主）列表
     *
     * 查询指定患者就诊期间的所有常规检验记录主表信息，包括血液检验、
     * 尿液检验、生化检验、免疫检验等各类实验室检验项目。
     * 检验记录主表包含检验的基本信息，如检验大项、标本类型、报告时间等，
     * 为医生提供患者的完整检验历史概览。
     *
     * @param visitSn 单次就诊唯一标识号，用于筛选特定就诊的检验记录
     * @return List<B171DTO> 常规检验记录（主）列表，包含该就诊的所有检验记录主要信息：
     *         - labSn: 检验流水号，检验记录的唯一标识
     *         - labType: 检验大项（如：血常规、尿常规、生化全套等）
     *         - specimenTypeName: 标本类型（如：血液、尿液、痰液等）
     *         - reportDatetime: 报告时间，检验结果出具的时间
     *         如果该就诊没有检验记录，返回空列表
     */
    @GetMapping("/list/{visitSn}")
    public List<B171DTO> list(@PathVariable String visitSn) {
        List<B171> list = b171Service.lambdaQuery().eq(B171::getVisitSn,visitSn).list();
        return list.stream().map(b171Converter::toDto).toList();
    }

}