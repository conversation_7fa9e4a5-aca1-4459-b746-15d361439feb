package cn.wbw.yyhis.controller;

import org.springframework.web.bind.annotation.*;
import org.springframework.beans.factory.annotation.Autowired;
import cn.wbw.yyhis.model.dto.B1711DTO;
import cn.wbw.yyhis.model.entity.B1711;
import cn.wbw.yyhis.service.B1711Service;
import cn.wbw.yyhis.converter.B1711Converter;

import java.util.List;

/**
 * 常规检验记录（明细）管理接口
 *
 * 提供患者常规检验记录明细表的查询功能，包括血液检验、尿液检验、生化检验等
 * 各类实验室检验项目的详细结果信息管理。检验明细记录包含具体的检验项目、
 * 检验结果、参考值等详细信息，为医生提供精确的实验室数据支持，
 * 是临床诊断和治疗监测的重要依据。
 *
 * <AUTHOR>
 * @since 2024-07-26
 */
@RestController
@RequestMapping("/b1711")
public class B1711Controller {

    @Autowired
    private B1711Service b1711Service;

    @Autowired
    private B1711Converter b1711Converter;

    /**
     * 根据检验流水号查询常规检验记录明细列表
     *
     * 查询指定检验流水号对应的所有检验明细记录，包括具体的检验项目、
     * 检验结果、参考值等详细信息。检验明细记录提供了检验的具体数据，
     * 每个检验大项下包含多个具体的检验指标，为医生提供详细的实验室数据。
     *
     * @param labSn 检验流水号，对应B171（常规检验记录主表）的主键，
     *              用于关联获取该次检验的所有明细项目
     * @return List<B1711DTO> 常规检验记录明细列表，包含该检验的所有明细项目：
     *         - itemName: 检验明细项目名称（如：白细胞计数、血红蛋白等）
     *         - resultHybrid: 检验结果（未区分定性/定量）
     *         - resultQualitative: 检验结果（定性），如阳性/阴性
     *         - resultQuantitative: 检验结果（数值），具体的数值结果
     *         - resultUnit: 检验计量单位（如：10^9/L、g/L等）
     *         - referenceRangeHigh: 参考值上限
     *         - referenceRangeLow: 参考值下限
     *         如果该检验流水号没有明细记录，返回空列表
     */
    @GetMapping("/list/{labSn}")
    public List<B1711DTO> list(@PathVariable String labSn) {
        List<B1711> list = b1711Service.lambdaQuery().eq(B1711::getLabSn,labSn).list();
        return list.stream().map(b1711Converter::entityToDto).toList();
    }
}