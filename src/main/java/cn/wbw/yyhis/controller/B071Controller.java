package cn.wbw.yyhis.controller;

import cn.hutool.core.collection.CollectionUtil;
import cn.wbw.yyhis.common.ApiResult;
import cn.wbw.yyhis.converter.B071Converter;
import cn.wbw.yyhis.model.dto.B071DTO;
import cn.wbw.yyhis.model.dto.B071UpsertDTO;
import cn.wbw.yyhis.model.entity.B023;
import cn.wbw.yyhis.model.entity.B071;
import cn.wbw.yyhis.service.B023Service;
import cn.wbw.yyhis.service.B071Service;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.RequiredArgsConstructor;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 出院记录管理接口
 *
 * 提供患者出院记录的完整管理功能，包括出院记录的创建、更新、删除和查询。
 * 出院记录是患者住院治疗结束时的重要医疗文档，包含入院诊断、出院诊断、
 * 入院情况、诊疗经过、出院情况、出院医嘱等关键信息，是医疗质量管理和
 * 患者后续治疗的重要依据。
 *
 * <AUTHOR>
 * @since 2024-07-30
 */
@RestController
@RequestMapping("/b071")
@RequiredArgsConstructor
public class B071Controller {

    private final B071Service b071Service;
    private final B023Service b023Service;
    private final B071Converter b071Converter = B071Converter.INSTANCE;

    /**
     * 保存或更新出院记录
     *
     * 根据是否存在记录流水号自动判断执行新增或更新操作。
     * 出院记录是患者住院治疗的总结性文档，记录了患者从入院到出院的
     * 完整诊疗过程，包括诊断变化、治疗效果、出院指导等重要信息。
     *
     * @param dto 出院记录数据传输对象，包含以下主要字段：
     *            - recordSn: 病程记录流水号（更新时必填，新增时可为空）
     *            - visitSn: 单次就诊唯一标识号（必填）
     *            - admissionDiag: 入院诊断，患者入院时的诊断
     *            - dischargeDiag: 出院诊断，患者出院时的最终诊断
     *            - admissionCondition: 入院情况，患者入院时的病情描述
     *            - treatmentInfo: 诊疗经过，住院期间的治疗过程和效果
     *            - dischargeCondition: 出院情况，患者出院时的病情状态
     *            - dischargeOrder: 出院医嘱，出院后的用药指导和注意事项
     * @return ApiResult 统一返回结果，包含操作状态和出院记录信息
     */
    @PostMapping("/save")
    public ApiResult save(@RequestBody B071UpsertDTO dto) {
        if (!StringUtils.hasText(dto.getRecordSn())) {
            return b071Service.addDischargeRecord(dto);
        } else {
            return b071Service.updateDischargeRecord(dto);
        }
    }

    /**
     * 根据就诊流水号删除出院记录
     *
     * 删除指定患者的出院记录。删除操作会彻底移除该出院记录，
     * 请谨慎操作，建议在删除前确认该记录不再需要或已有备份。
     *
     * @param visitSn 单次就诊唯一标识号，用于定位要删除的出院记录
     * @return Boolean 删除操作结果，true表示删除成功，false表示删除失败
     */
    @DeleteMapping("/delete/{visitSn}")
    public Boolean delete(@PathVariable String visitSn) {
        return b071Service.deleteByVisitSn(visitSn);
    }

    /**
     * 根据就诊流水号查询出院记录详情
     *
     * 查询指定患者的出院记录详细信息，系统会自动关联查询该患者的
     * 入院诊断和出院诊断信息，并将多个诊断名称合并显示。
     * 提供完整的出院记录信息，包括诊断、治疗过程、出院指导等。
     *
     * @param visitSn 单次就诊唯一标识号，用于定位出院记录
     * @return B071DTO 出院记录详细信息对象，包含：
     *         - admissionDiag: 入院诊断（自动关联诊断记录）
     *         - dischargeDiag: 出院诊断（自动关联诊断记录）
     *         - admissionCondition: 入院情况
     *         - treatmentInfo: 诊疗经过
     *         - dischargeCondition: 出院情况
     *         - dischargeOrder: 出院医嘱
     *         - admissionDatetime: 入院时间
     *         - dischargeDatetime: 出院时间
     *         - lengthOfStay: 住院天数
     *         - recordDatetime: 记录创建时间
     *         - signatureDoctor: 医生签名
     *         - 其他系统字段和扩展字段
     *         如果未找到对应记录，返回null
     */
    @GetMapping("/detail/{visitSn}")
    public B071DTO detail(@PathVariable String visitSn) {
        B071 one = b071Service.lambdaQuery().eq(B071::getVisitSn, visitSn).one();
        if (one != null) {
            // 一次查询获取所有诊断数据，按类型分组
            Map<String, List<B023>> diagnosisMap = b023Service.lambdaQuery()
                    .eq(B023::getVisitSn, visitSn)
                    .in(B023::getDiagType, "入院诊断", "出院诊断")
                    .select(B023::getDiagName, B023::getDiagType)
                    .list()
                    .stream()
                    .collect(Collectors.groupingBy(B023::getDiagType));

            // 设置入院诊断
            String admissionDiag = buildDiagnosisString(diagnosisMap.get("入院诊断"));
            one.setAdmissionDiag(admissionDiag);

            // 设置出院诊断
            String dischargeDiag = buildDiagnosisString(diagnosisMap.get("出院诊断"));
            one.setDischargeDiag(dischargeDiag);
        }
        return b071Converter.toDto(one);
    }

    /**
     * 查询所有出院记录
     *
     * 获取系统中所有出院记录的完整信息列表，
     * 适用于出院记录的全量查询和数据导出功能。
     *
     * @return List<B071DTO> 所有出院记录信息列表，包含出院记录的完整信息
     */
    @GetMapping("/all")
    public List<B071DTO> findAll() {
        List<B071> list = b071Service.list();
        return list.stream().map(b071Converter::toDto).toList();
    }

    /**
     * 分页查询出院记录
     *
     * 分页获取出院记录信息，支持自定义页码和每页数量，
     * 适用于出院记录的列表展示和管理功能。
     *
     * @param pageNum 页码，默认值为1，表示第一页
     * @param pageSize 每页记录数，默认值为10
     * @return IPage<B071DTO> 分页结果对象，包含：
     *         - records: 当前页的出院记录列表
     *         - current: 当前页码
     *         - size: 每页大小
     *         - total: 总记录数
     *         - pages: 总页数
     */
    @GetMapping("/page")
    public IPage<B071DTO> findPage(@RequestParam(defaultValue = "1") Integer pageNum,
                                   @RequestParam(defaultValue = "10") Integer pageSize) {
        Page<B071> page = new Page<>(pageNum, pageSize);
        Page<B071> b071Page = b071Service.page(page);
        IPage<B071DTO> dtoPage = b071Page.convert(b071Converter::toDto);
        return dtoPage;
    }

    /**
     * 构建诊断字符串
     *
     * 将诊断列表转换为用逗号分隔的诊断字符串，
     * 用于在出院记录中显示多个诊断信息。
     *
     * @param diagnosisList 诊断列表
     * @return 诊断字符串，多个诊断用"，"分隔，如果列表为空则返回空字符串
     */
    private String buildDiagnosisString(List<B023> diagnosisList) {
        if (CollectionUtil.isEmpty(diagnosisList)) {
            return "";
        }
        return diagnosisList.stream()
                .map(B023::getDiagName)
                .collect(Collectors.joining("，"));
    }
}