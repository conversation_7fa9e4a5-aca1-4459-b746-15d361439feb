package cn.wbw.yyhis.controller;

import cn.wbw.yyhis.converter.B0615Converter;
import cn.wbw.yyhis.model.dto.B0615DTO;
import cn.wbw.yyhis.model.dto.B0615UpsertDTO;
import cn.wbw.yyhis.model.entity.B0615;
import cn.wbw.yyhis.service.B0615Service;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.RequiredArgsConstructor;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 有创操作记录管理接口
 *
 * 提供患者有创操作记录的管理功能，包括各种有创医疗操作的记录管理。
 * 有创操作记录是医疗安全管理的重要文档，详细记录了有创操作的过程、
 * 操作要点、注意事项等关键信息，为医疗质量控制和风险管理提供重要依据。
 *
 * <AUTHOR>
 * @since 2024-07-30
 */
@RestController
@RequestMapping("/b0615")
@RequiredArgsConstructor
public class B0615Controller {

    private final B0615Service b0615Service;
    private final B0615Converter converter = B0615Converter.INSTANCE;

    /**
     * 保存或更新有创操作记录
     *
     * 根据是否提供记录流水号来判断是新增还是更新操作。
     * 有创操作记录是医疗安全管理的重要文档，详细记录了有创操作的
     * 全过程，包括操作步骤、注意事项等关键信息。
     *
     * @param dto 有创操作记录数据传输对象，包含以下主要字段：
     *            - recordSn: 病程记录流水号（更新时必填，新增时可为空）
     *            - visitSn: 单次就诊唯一标识号（必填）
     *            - perfromDatetime: 操作日期，进行有创操作的具体时间
     *            - procedureName: 操作名称，具体的有创操作名称
     *            - recordContent: 操作记录，详细的操作过程记录
     *            - mattersNeedingAttention: 注意事项，操作中需要注意的问题
     * @return B0615DTO 保存后的有创操作记录完整信息
     */
    @PostMapping("/save")
    public B0615DTO save(@RequestBody B0615UpsertDTO dto) {
        if (!StringUtils.hasText(dto.getRecordSn())) {
            B0615 newB0615 = b0615Service.addProcedureRecord(dto);
            return converter.toDto(newB0615);
        } else {
            b0615Service.updateProcedureRecord(dto);
            return converter.toDto(b0615Service.getById(dto.getRecordSn()));
        }
    }

    /**
     * 根据就诊流水号删除有创操作记录
     *
     * 删除指定就诊的有创操作记录。删除有创操作记录需要谨慎操作，
     * 因为这是医疗安全管理的重要文档，记录了有创操作的详细过程，
     * 建议在删除前确认该记录确实需要删除且已有备份。
     *
     * @param visitSn 单次就诊唯一标识号
     * @return Boolean 删除操作结果，true表示删除成功，false表示删除失败
     */
    @DeleteMapping("/delete/{visitSn}")
    public Boolean delete(@PathVariable String visitSn) {
        return b0615Service.deleteByVisitSn(visitSn);
    }

    /**
     * 查询所有有创操作记录
     *
     * 获取系统中的所有有创操作记录，用于统计分析或管理查看。
     * 该接口返回完整的有创操作记录列表，包含所有患者的操作信息。
     *
     * @return List<B0615DTO> 所有有创操作记录列表，包含：
     *         - recordSn: 病程记录流水号
     *         - visitSn: 就诊流水号
     *         - perfromDatetime: 操作时间
     *         - procedureName: 操作名称
     *         - recordContent: 操作记录
     *         - mattersNeedingAttention: 注意事项
     *         - signatureDoctor: 签名医生
     *         - recordDatetime: 记录创建时间
     */
    @GetMapping("/all")
    public List<B0615DTO> findAll() {
        List<B0615> list = b0615Service.list();
        return list.stream().map(converter::toDto).collect(Collectors.toList());
    }

    /**
     * 根据就诊流水号查询有创操作记录详情
     *
     * 查询指定就诊的有创操作记录详细信息，包含完整的操作过程记录。
     *
     * @param visitSn 单次就诊唯一标识号
     * @return B0615DTO 有创操作记录详细信息，包含：
     *         - recordSn: 病程记录流水号
     *         - visitSn: 就诊流水号
     *         - perfromDatetime: 操作日期
     *         - procedureName: 操作名称
     *         - recordContent: 操作记录详情
     *         - mattersNeedingAttention: 注意事项
     *         - signatureDoctor: 签名医生
     *         - recordDatetime: 记录创建时间
     *         如果该就诊没有有创操作记录，返回null
     */
    @GetMapping("/detail/{visitSn}")
    public B0615DTO detail(@PathVariable String visitSn) {
        B0615 b0615 = b0615Service.lambdaQuery().eq(B0615::getVisitSn, visitSn).one();
        return converter.toDto(b0615);
    }

    /**
     * 分页查询有创操作记录
     *
     * 分页获取有创操作记录列表，支持大数据量的操作记录查询和展示。
     * 适用于有创操作记录的管理界面和统计分析。
     *
     * @param pageNum 页码，从1开始，默认值为1
     * @param pageSize 每页记录数，默认值为10
     * @return Page<B0615DTO> 分页的有创操作记录列表，包含：
     *         - current: 当前页码
     *         - size: 每页大小
     *         - total: 总记录数
     *         - records: 当前页的有创操作记录列表，每条记录包含完整的操作信息
     */
    @GetMapping("/page")
    public Page<B0615DTO> findPage(@RequestParam(defaultValue = "1") Integer pageNum,
                                   @RequestParam(defaultValue = "10") Integer pageSize) {
        Page<B0615> page = new Page<>(pageNum, pageSize);
        Page<B0615> entityPage = b0615Service.page(page);
        Page<B0615DTO> dtoPage = new Page<>(entityPage.getCurrent(), entityPage.getSize(), entityPage.getTotal());
        dtoPage.setRecords(entityPage.getRecords().stream().map(converter::toDto).collect(Collectors.toList()));
        return dtoPage;
    }
} 