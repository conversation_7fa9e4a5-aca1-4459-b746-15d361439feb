package cn.wbw.yyhis.controller;

import cn.wbw.yyhis.converter.B021Converter;
import cn.wbw.yyhis.model.dto.B021DTO;
import cn.wbw.yyhis.model.dto.History;
import cn.wbw.yyhis.model.dto.integration.HospitalDataPacket;
import cn.wbw.yyhis.model.entity.B021;
import cn.wbw.yyhis.service.B021Service;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.Collections;

/**
 * 患者就诊基本信息管理接口
 *
 * 提供患者就诊基本信息的查询功能，包括患者的基本信息、就诊信息、
 * 血型信息、联系方式等完整的就诊记录数据。
 *
 * <AUTHOR>
 * @since 2024-07-30
 */
@RestController
@RequestMapping("/b021")
@RequiredArgsConstructor
public class B021Controller {

    private final B021Service b021Service;
    private final B021Converter converter = B021Converter.INSTANCE;

    /**
     * 根据就诊流水号查询患者就诊基本信息
     *
     * 通过单次就诊唯一标识号查询患者的完整就诊基本信息，包括：
     * - 患者基本信息（姓名、性别、年龄、证件信息等）
     * - 就诊信息（就诊时间、就诊医生、就诊类型等）
     * - 血型信息（ABO血型、Rh血型等）
     * - 联系方式（本人电话、紧急联系人等）
     * - 地址信息（户籍地址、现住址等）
     * - 其他相关信息（入院时间、出院时间、就诊状态等）
     *
     * @param visitSn 单次就诊唯一标识号，必填参数，用于唯一标识一次就诊记录
     * @return History 患者就诊历史信息对象，包含患者ID、就诊流水号和数据包
     *         - patientId: 患者唯一标识
     *         - visitSn: 就诊流水号
     *         - dataPacket: 数据包列表，包含表代码和具体的就诊基本信息数据
     *         如果未找到对应的就诊记录，返回空的History对象
     */
    @GetMapping("/history")
    public History history(@RequestParam String visitSn) {
        History history = new History();
        B021 one = b021Service.lambdaQuery().eq(B021::getVisitSn, visitSn).one();
        if (one == null) {
            return history;
        }
        B021DTO b021DTO = converter.entityToDto(one);
        history.setPatientId(one.getPatientId());
        history.setVisitSn(one.getVisitSn());
        HospitalDataPacket dataPacket = new HospitalDataPacket();
        dataPacket.setTableCode(B021.TABLE_NAME);
        dataPacket.setData(Collections.singletonList(b021DTO));
        history.setDataPacket(Collections.singletonList(dataPacket));

        return history;
    }

}
