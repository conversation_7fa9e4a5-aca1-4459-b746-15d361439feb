package cn.wbw.yyhis.controller;

import cn.hutool.core.util.StrUtil;
import cn.wbw.yyhis.converter.B061Converter;
import cn.wbw.yyhis.model.dto.B061DTO;
import cn.wbw.yyhis.model.dto.B061UpsertDTO;
import cn.wbw.yyhis.model.entity.B061;
import cn.wbw.yyhis.service.B061Service;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 入院病程记录管理接口
 *
 * 提供患者入院病程记录的完整管理功能，包括病程记录的创建、更新、删除和查询。
 * 入院病程记录是医生对患者住院期间病情变化、治疗过程、医疗决策等的详细记录，
 * 是重要的医疗文档，用于记录患者的诊疗过程和医疗质量管理。
 *
 * <AUTHOR>
 * @since 2024-07-30
 */
@RestController
@RequestMapping("/b061")
@RequiredArgsConstructor
public class B061Controller {

    private final B061Service b061Service;
    private final B061Converter b061Converter = B061Converter.INSTANCE;

    /**
     * 保存或更新入院病程记录
     *
     * 根据是否存在记录流水号自动判断执行新增或更新操作。
     * 病程记录是医生对患者病情变化、治疗效果、医疗决策等的详细记录，
     * 包含记录内容、记录类型、医生签名等重要信息。
     *
     * @param dto 入院病程记录数据传输对象，包含以下主要字段：
     *            - recordSn: 病程记录流水号（更新时必填，新增时可为空）
     *            - visitSn: 单次就诊唯一标识号（必填）
     *            - recordContent: 记录内容，病程记录的核心内容，
     *              包括患者病情变化、治疗措施、医疗决策、观察要点等详细信息
     * @return B061DTO 保存成功的病程记录完整信息，包含：
     *         - recordSn: 系统生成的记录流水号
     *         - visitSn: 就诊流水号
     *         - recordContent: 记录内容
     *         - recordDatetime: 记录创建时间
     *         - recordTitle: 病程记录标题
     *         - recordType: 病程记录类型
     *         - signatureDoctor: 医生签名
     *         - 其他系统字段和扩展字段
     */
    @PostMapping("/save")
    public B061DTO save(@RequestBody B061UpsertDTO dto) {
        if (StrUtil.isEmpty(dto.getRecordSn())) {
            // 新增
            B061 newB061 = b061Service.addAdmissionRecord(dto);
            return b061Converter.toDto(newB061);
        } else {
            // 更新
            b061Service.updateAdmissionRecord(dto);
            return b061Converter.toDto(b061Service.getById(dto.getRecordSn()));
        }
    }

    /**
     * 根据记录流水号删除入院病程记录
     *
     * 删除指定的病程记录。删除操作会彻底移除该病程记录，
     * 请谨慎操作，建议在删除前确认该记录不再需要或已有备份。
     *
     * @param recordSn 病程记录流水号，用于唯一标识一条病程记录
     * @return Boolean 删除操作结果，true表示删除成功，false表示删除失败
     */
    @DeleteMapping("/delete/{recordSn}")
    public Boolean delete(@PathVariable String recordSn) {
        return b061Service.deleteByRecordSn(recordSn);
    }

    /**
     * 根据记录流水号查询入院病程记录详情
     *
     * 查询指定病程记录的详细信息，包含记录内容、创建时间、
     * 医生签名、记录类型等完整的病程记录信息。
     *
     * @param recordSn 病程记录流水号，用于定位具体的病程记录
     * @return B061DTO 病程记录详细信息对象，包含：
     *         - recordContent: 病程记录内容
     *         - recordDatetime: 记录创建时间
     *         - recordTitle: 记录标题
     *         - recordType: 记录类型
     *         - signatureDoctor: 医生签名
     *         - patientId: 患者ID
     *         - visitSn: 就诊流水号
     *         - hospitalCode: 医院代码
     *         - 其他相关字段
     *         如果未找到对应记录，返回null
     */
    @GetMapping("/detail/{recordSn}")
    public B061DTO findById(@PathVariable String recordSn) {
        B061 b061 = b061Service.getById(recordSn);
        return b061Converter.toDto(b061);
    }

    /**
     * 根据就诊流水号查询入院病程记录列表
     *
     * 查询指定患者就诊期间的所有病程记录，按记录创建时间倒序排列，
     * 最新的病程记录排在前面。适用于查看患者的完整病程记录历史。
     *
     * @param visitSn 单次就诊唯一标识号，用于筛选特定就诊的病程记录
     * @return List<B061DTO> 病程记录列表，包含该就诊的所有病程记录，
     *         每条记录包含记录内容、时间、医生等完整信息。
     *         如果该就诊没有病程记录，返回空列表
     */
    @GetMapping("/list")
    public List<B061DTO> findByVisitSn(@RequestParam String visitSn) {
        List<B061> list = b061Service.lambdaQuery().eq(B061::getVisitSn,visitSn)
                .orderByDesc(B061::getRecordDatetime)
                .list();
        return list.stream().map(b061Converter::toDto).toList();
    }

    /**
     * 根据就诊流水号统计病程记录数量
     *
     * 统计指定患者就诊期间的病程记录总数，
     * 用于了解该患者的病程记录完整性和医疗记录的详细程度。
     *
     * @param visitSn 单次就诊唯一标识号
     * @return Long 该就诊的病程记录总数量
     */
    @GetMapping("/count/{visitSn}")
    public Long countByVisitSn(@PathVariable String visitSn) {
        return b061Service.countByVisitSn(visitSn);
    }
}