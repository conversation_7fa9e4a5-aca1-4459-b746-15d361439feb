package cn.wbw.yyhis.controller;

import cn.hutool.core.collection.CollectionUtil;
import cn.wbw.yyhis.converter.B0614Converter;
import cn.wbw.yyhis.model.dto.B0614DTO;
import cn.wbw.yyhis.model.dto.B0614UpsertDTO;
import cn.wbw.yyhis.model.entity.B023;
import cn.wbw.yyhis.model.entity.B0614;
import cn.wbw.yyhis.service.B023Service;
import cn.wbw.yyhis.service.B0614Service;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.RequiredArgsConstructor;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 术前小结记录管理接口
 *
 * 提供患者术前小结记录的管理功能，包括手术前的诊断总结、手术方案确定等
 * 重要医疗文档的管理。术前小结是手术前的重要总结性文档，
 * 汇总了患者的诊断信息、手术指征、拟施手术方案等关键信息，
 * 为手术的顺利进行提供完整的医疗依据。
 *
 * <AUTHOR>
 * @since 2024-07-30
 */
@RestController
@RequestMapping("/b0614")
@RequiredArgsConstructor
public class B0614Controller {

    private final B0614Service b0614Service;
    private final B023Service b023Service;
    private final B0614Converter b0614Converter = B0614Converter.INSTANCE;

    /**
     * 保存或更新术前小结记录
     *
     * 根据是否提供记录流水号来判断是新增还是更新操作。
     * 术前小结是手术前的重要总结性文档，汇总了患者的诊断信息、
     * 手术指征、拟施手术方案等关键信息。
     *
     * @param dto 术前小结记录数据传输对象，包含以下主要字段：
     *            - recordSn: 病程记录流水号（更新时必填，新增时可为空）
     *            - visitSn: 单次就诊唯一标识号（必填）
     *            - preOperativeDiagnosis: 术前诊断，手术前确定的诊断
     *            - diagnosisBasis: 诊断依据，支持诊断的客观证据
     *            - operationIndication: 手术指征，进行手术的医学依据
     *            - plannedSurgeryName: 拟施手术名称，计划进行的手术
     *            - surgeryMethod: 拟施手术方式，具体的手术方法
     *            - anesthesiaMethod: 拟施麻醉方式，计划采用的麻醉方法
     *            - possibleProblemsAndSolution: 注意事项，手术中需要注意的问题
     * @return B0614DTO 保存后的术前小结记录完整信息
     */
    @PostMapping("/save")
    public B0614DTO save(@RequestBody B0614UpsertDTO dto) {
        if (!StringUtils.hasText(dto.getRecordSn())) {
            B0614 newB0614 = b0614Service.addPreoperativeSummary(dto);
            return b0614Converter.toDto(newB0614);
        } else {
            b0614Service.updatePreoperativeSummary(dto);
            return b0614Converter.toDto(b0614Service.getById(dto.getRecordSn()));
        }
    }

    /**
     * 根据就诊流水号删除术前小结记录
     *
     * 删除指定就诊的术前小结记录。删除术前小结记录需要谨慎操作，
     * 因为这是手术前的重要总结性文档，记录了手术决策的关键信息，
     * 建议在删除前确认该记录确实需要删除且已有备份。
     *
     * @param visitSn 单次就诊唯一标识号
     * @return Boolean 删除操作结果，true表示删除成功，false表示删除失败
     */
    @DeleteMapping("/delete/{visitSn}")
    public Boolean delete(@PathVariable String visitSn) {
        return b0614Service.deleteByVisitSn(visitSn);
    }

    /**
     * 根据就诊流水号查询术前小结记录详情
     *
     * 查询指定就诊的术前小结记录详细信息，并自动关联术前诊断信息。
     * 该接口会自动从诊断记录表中获取该就诊的术前诊断，
     * 并将诊断名称组合后填充到术前诊断字段中。
     *
     * @param visitSn 单次就诊唯一标识号
     * @return B0614DTO 术前小结记录详细信息，包含：
     *         - recordSn: 病程记录流水号
     *         - visitSn: 就诊流水号
     *         - preOperativeDiagnosis: 术前诊断（自动从诊断表关联获取）
     *         - diagnosisBasis: 诊断依据
     *         - operationIndication: 手术指征
     *         - plannedSurgeryName: 拟施手术名称
     *         - surgeryMethod: 拟施手术方式
     *         - anesthesiaMethod: 拟施麻醉方式
     *         - possibleProblemsAndSolution: 注意事项
     *         - signatureDoctor: 签名医生
     *         - recordDatetime: 记录创建时间
     *         如果该就诊没有术前小结记录，返回null
     */
    @GetMapping("/detail/{visitSn}")
    public B0614DTO detail(@PathVariable String visitSn) {
        B0614 one = b0614Service.lambdaQuery().eq(B0614::getVisitSn, visitSn).one();
        if (one != null) {
            List<B023> list = b023Service.lambdaQuery()
                    .eq(B023::getVisitSn, visitSn)
                    .eq(B023::getDiagType, "术前诊断").select(B023::getDiagName).list();
            String diagnosis ="";
            if (CollectionUtil.isNotEmpty(list)) {
                diagnosis = list.stream().map(B023::getDiagName).collect(Collectors.joining("，"));
            }
            one.setPreOperativeDiagnosis(diagnosis);
        }
        return b0614Converter.toDto(one);
    }

    /**
     * 查询所有术前小结记录
     *
     * 获取系统中的所有术前小结记录，用于统计分析或管理查看。
     * 该接口返回完整的术前小结记录列表，包含所有患者的小结信息。
     *
     * @return List<B0614DTO> 所有术前小结记录列表，包含：
     *         - recordSn: 病程记录流水号
     *         - visitSn: 就诊流水号
     *         - preOperativeDiagnosis: 术前诊断
     *         - operationIndication: 手术指征
     *         - plannedSurgeryName: 拟施手术名称
     *         - surgeryMethod: 手术方式
     *         - anesthesiaMethod: 麻醉方式
     *         - recordDatetime: 记录创建时间
     */
    @GetMapping("/all")
    public List<B0614DTO> findAll() {
        List<B0614> list = b0614Service.list();
        return list.stream().map(b0614Converter::toDto).toList();
    }

    /**
     * 分页查询术前小结记录
     *
     * 分页获取术前小结记录列表，支持大数据量的小结记录查询和展示。
     * 适用于术前小结记录的管理界面和统计分析。
     *
     * @param pageNum 页码，从1开始，默认值为1
     * @param pageSize 每页记录数，默认值为10
     * @return IPage<B0614DTO> 分页的术前小结记录列表，包含：
     *         - current: 当前页码
     *         - size: 每页大小
     *         - total: 总记录数
     *         - records: 当前页的术前小结记录列表，每条记录包含完整的小结信息
     */
    @GetMapping("/page")
    public IPage<B0614DTO> findPage(@RequestParam(defaultValue = "1") Integer pageNum,
                                               @RequestParam(defaultValue = "10") Integer pageSize) {
        Page<B0614> page = new Page<>(pageNum, pageSize);
        Page<B0614> b0614Page = b0614Service.page(page);
        IPage<B0614DTO> dtoPage = b0614Page.convert(b0614Converter::toDto);
        return dtoPage;
    }
} 