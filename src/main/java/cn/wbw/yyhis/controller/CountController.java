package cn.wbw.yyhis.controller;

import cn.wbw.yyhis.model.dto.CountDTO;
import cn.wbw.yyhis.model.entity.*;
import cn.wbw.yyhis.service.*;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

/**
 * 医疗记录统计接口
 *
 * 提供患者就诊相关医疗记录的数量统计功能，包括病程记录、检查记录、检验记录等
 * 各类医疗文档的统计查询。统计信息为医疗管理人员和临床医生提供患者
 * 医疗记录的概览信息，便于快速了解患者的医疗文档完整性。
 *
 * <AUTHOR>
 * @since 2024-07-30
 */
@RestController
@RequestMapping("/count")
@RequiredArgsConstructor
public class CountController {

    private final B061Service b061Service;
    private final B161Service b161service;
    private final B162Service b162service;
    private final B163Service b163service;
    private final B171Service b171service;

    /**
     * 根据就诊流水号统计患者医疗记录数量
     *
     * 统计指定患者就诊期间的各类医疗记录数量，包括病程记录、检查记录、
     * 检验记录等。统计结果为医疗管理提供数据支持，帮助评估患者
     * 医疗文档的完整性和医疗服务的全面性。
     *
     * @param visitSn 单次就诊唯一标识号，用于筛选特定就诊的医疗记录
     * @return CountDTO 医疗记录数量统计结果，包含：
     *         - b061Count: 入院病程记录数量，患者住院期间的病程记录总数
     *         - b161Count: 常规检查记录数量，包括影像学检查、超声检查等
     *         - b162Count: 病理检查记录数量，包括组织病理学、细胞病理学检查
     *         - b163Count: 分子病理检测记录数量，包括基因检测、分子标志物检测
     *         - b171Count: 常规检验记录数量，包括血液检验、尿液检验、生化检验等
     *         各项统计数据均为该就诊的实际记录数量，如果某类记录不存在则返回0
     */
    @GetMapping
    public CountDTO count(@RequestParam String visitSn) {
        CountDTO countDTO = new CountDTO();

        Long count = b061Service.lambdaQuery().eq(B061::getVisitSn, visitSn).count();
        countDTO.setB061Count(count);
        count = b161service.lambdaQuery().eq(B161::getVisitSn, visitSn).count();
        countDTO.setB161Count(count);
        count = b162service.lambdaQuery().eq(B162::getVisitSn, visitSn).count();
        countDTO.setB162Count(count);
        count = b163service.lambdaQuery().eq(B163::getVisitSn, visitSn).count();
        countDTO.setB163Count(count);
        count = b171service.lambdaQuery().eq(B171::getVisitSn, visitSn).count();
        countDTO.setB171Count(count);

        return countDTO;
    }

}