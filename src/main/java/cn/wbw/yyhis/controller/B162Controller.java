package cn.wbw.yyhis.controller;

import org.springframework.web.bind.annotation.*;
import org.springframework.beans.factory.annotation.Autowired;
import cn.wbw.yyhis.model.dto.B162DTO;
import cn.wbw.yyhis.model.entity.B162;
import cn.wbw.yyhis.service.B162Service;
import cn.wbw.yyhis.converter.B162Converter;

import java.util.List;

/**
 * 病理检查记录管理接口
 *
 * 提供患者病理检查记录的管理功能，包括组织病理学检查、细胞病理学检查等
 * 病理检查项目的记录管理。病理检查是肿瘤诊断的金标准，
 * 为临床提供准确的病理诊断和分级分期信息，是制定治疗方案的重要依据。
 *
 * <AUTHOR>
 * @since 2024-07-26
 */
@RestController
@RequestMapping("/b162")
public class B162Controller {

    @Autowired
    private B162Service b162Service;

    @Autowired
    private B162Converter b162Converter;

    /**
     * 更新病理检查记录
     *
     * 更新指定的病理检查记录信息，可以修改病理诊断结论、
     * 肉眼所见描述、病理检查类型等字段。病理记录的更新需要
     * 严格的权限控制，确保病理诊断的准确性和权威性。
     *
     * @param b162DTO 病理检查记录数据传输对象，包含以下主要字段：
     *                - reportNo: 报告单号，病理检查记录的唯一标识
     *                - pathologyTestType: 病理检查类型（如：组织病理、细胞病理等）
     *                - reportDatetime: 报告时间
     *                - applyDatetime: 申请时间
     *                - grossDescription: 肉眼所见，标本的宏观描述
     *                - pathoDiagConclusion: 病理诊断结论，最终的病理诊断结果
     * @return Boolean 更新操作结果，true表示更新成功，false表示更新失败
     */
    @PutMapping("/update")
    public Boolean update(@RequestBody B162DTO b162DTO) {
        return b162Service.updateDto(b162DTO);
    }

    /**
     * 根据报告单号删除病理检查记录
     *
     * 删除指定的病理检查记录。删除病理记录需要极其谨慎，
     * 因为病理诊断是肿瘤诊断的金标准，是重要的医疗诊断依据。
     * 建议在删除前确认该记录确实需要删除且已有备份。
     *
     * @param id 报告单号，用于唯一标识一条病理检查记录
     * @return Boolean 删除操作结果，true表示删除成功，false表示删除失败
     */
    @DeleteMapping("/delete/{id}")
    public Boolean delete(@PathVariable("id") String id) {
        return b162Service.deleteByReportNo(id);
    }

    /**
     * 根据就诊流水号查询病理检查记录列表
     *
     * 查询指定患者就诊期间的所有病理检查记录，包括组织病理学检查、
     * 细胞病理学检查等。病理检查记录为医生提供准确的病理诊断信息，
     * 是肿瘤分期、分级和治疗方案制定的重要依据。
     *
     * @param visitSn 单次就诊唯一标识号，用于筛选特定就诊的病理检查记录
     * @return List<B162DTO> 病理检查记录列表，包含该就诊的所有病理检查记录：
     *         - reportNo: 报告单号
     *         - pathologyTestType: 病理检查类型
     *         - reportDatetime: 报告时间
     *         - applyDatetime: 申请时间
     *         - grossDescription: 肉眼所见描述
     *         - pathoDiagConclusion: 病理诊断结论
     *         如果该就诊没有病理检查记录，返回空列表
     */
    @GetMapping("/list/{visitSn}")
    public List<B162DTO> list(@PathVariable String visitSn) {
        List<B162> list = b162Service.lambdaQuery().eq(B162::getVisitSn, visitSn).list();
        return list.stream().map(b162Converter::entityToDto).toList();
    }
}