package cn.wbw.yyhis.controller;

import cn.wbw.yyhis.converter.B023Converter;
import cn.wbw.yyhis.model.dto.B023DTO;
import cn.wbw.yyhis.model.dto.B023UpsertDTO;
import cn.wbw.yyhis.model.entity.B023;
import cn.wbw.yyhis.service.B023Service;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.RequiredArgsConstructor;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 患者诊断记录管理接口
 *
 * 提供患者诊断记录的完整管理功能，包括诊断记录的增删改查操作。
 * 支持多种诊断类型（入院诊断、出院诊断、门诊诊断等）的管理，
 * 以及主要诊断标识、诊断编码、诊断说明等详细信息的维护。
 *
 * <AUTHOR>
 * @since 2024-07-30
 */
@RestController
@RequestMapping("/b023")
@RequiredArgsConstructor
public class B023Controller {

    private final B023Service b023Service;
    private final B023Converter converter = B023Converter.INSTANCE;

    /**
     * 添加患者诊断记录
     *
     * 创建新的患者诊断记录，包括诊断名称、诊断编码、诊断类型、
     * 是否主要诊断、诊断时间、诊断说明等完整信息。
     * 系统会自动生成诊断ID和设置数据来源。
     *
     * @param dto 诊断记录数据传输对象，包含以下主要字段：
     *            - visitSn: 单次就诊唯一标识号（必填）
     *            - diagName: 诊断名称（必填）
     *            - diagCode: 诊断编码
     *            - diagType: 诊断类型（如：入院诊断、出院诊断等）
     *            - maindiagMark: 是否主要诊断标识
     *            - diagDatetime: 诊断时间
     *            - diagExplanation: 诊断说明
     * @return B023DTO 创建成功的诊断记录完整信息，包含系统生成的诊断ID等字段
     */
    @PostMapping("/add")
    public B023DTO add(@RequestBody B023UpsertDTO dto) {
        B023 newB023 = b023Service.addPatientDiagnosis(dto);
        return converter.entityToDto(newB023);
    }

    /**
     * 根据复合主键删除诊断记录
     *
     * 通过诊断ID和诊断数据来源的复合主键删除指定的诊断记录。
     * 删除操作会彻底移除该诊断记录，请谨慎操作。
     *
     * @param diagId 诊断ID号，诊断记录的唯一标识
     * @param diagSource 诊断数据来源，标识诊断数据的来源系统或模块
     * @return Boolean 删除操作结果，true表示删除成功，false表示删除失败
     */
    @DeleteMapping("/delete/{diagId}/{diagSource}")
    public Boolean delete(@PathVariable String diagId, @PathVariable String diagSource) {
        return b023Service.removeByCompositeId(diagId, diagSource);
    }

    /**
     * 更新患者诊断记录
     *
     * 根据复合主键（诊断ID + 诊断数据来源）更新指定的诊断记录信息。
     * 可以更新诊断名称、诊断编码、诊断类型、诊断说明等字段。
     *
     * @param diagId 诊断ID号，用于定位要更新的诊断记录
     * @param diagSource 诊断数据来源，用于定位要更新的诊断记录
     * @param dto 更新数据传输对象，包含要更新的字段信息
     * @return Boolean 更新操作结果，true表示更新成功，false表示更新失败
     */
    @PutMapping("/update/{diagId}/{diagSource}")
    public Boolean update(@PathVariable String diagId, @PathVariable String diagSource, @RequestBody B023UpsertDTO dto) {
        dto.setDiagId(diagId);
        dto.setDiagSource(diagSource);
        return b023Service.updatePatientDiagnosis(dto);
    }

    /**
     * 根据就诊流水号查询诊断记录列表
     *
     * 查询指定就诊记录的所有诊断信息，支持按诊断类型进行筛选。
     * 结果按记录创建时间倒序排列，最新的诊断记录排在前面。
     *
     * @param visitSn 单次就诊唯一标识号，必填参数
     * @param diagType 诊断类型，可选参数，用于筛选特定类型的诊断记录
     *                 常见类型包括：入院诊断、出院诊断、门诊诊断、修正诊断等
     * @return List<B023DTO> 诊断记录列表，包含诊断名称、编码、类型、时间等完整信息
     *         如果未找到匹配的诊断记录，返回空列表
     */
    @GetMapping("/list")
    public List<B023DTO> listByVisitSn(@RequestParam String visitSn, @RequestParam(required = false) String diagType) {
        List<B023> list = b023Service.lambdaQuery().eq(B023::getVisitSn, visitSn)
                .eq(StringUtils.hasText(diagType), B023::getDiagType, diagType)
                .orderByDesc(B023::getRecordDatetime)
                .list();
        return converter.entityListToDtoList(list);
    }

    /**
     * 根据复合主键查询诊断记录详情
     *
     * 通过诊断ID和诊断数据来源查询指定诊断记录的详细信息，
     * 包含诊断的所有字段信息。
     *
     * @param diagId 诊断ID号，诊断记录的唯一标识
     * @param diagSource 诊断数据来源，标识诊断数据的来源系统
     * @return B023DTO 诊断记录详细信息，包含诊断名称、编码、类型、医生信息等
     *         如果未找到对应记录，返回null
     */
    @GetMapping("/detail/{diagId}/{diagSource}")
    public B023DTO findById(@PathVariable String diagId, @PathVariable String diagSource) {
        return converter.entityToDto(b023Service.getByCompositeId(diagId, diagSource));
    }

    /**
     * 分页查询诊断记录
     *
     * 分页获取所有诊断记录信息，支持自定义页码和每页数量。
     * 适用于诊断记录的列表展示和管理功能。
     *
     * @param pageNum 页码，默认值为1，表示第一页
     * @param pageSize 每页记录数，默认值为10
     * @return Page<B023DTO> 分页结果对象，包含：
     *         - records: 当前页的诊断记录列表
     *         - current: 当前页码
     *         - size: 每页大小
     *         - total: 总记录数
     *         - pages: 总页数
     */
    @GetMapping("/page")
    public Page<B023DTO> findPage(@RequestParam(defaultValue = "1") Integer pageNum,
                                  @RequestParam(defaultValue = "10") Integer pageSize) {
        Page<B023> page = new Page<>(pageNum, pageSize);
        Page<B023> entityPage = b023Service.page(page);
        Page<B023DTO> dtoPage = new Page<>(entityPage.getCurrent(), entityPage.getSize(), entityPage.getTotal());
        dtoPage.setRecords(converter.entityListToDtoList(entityPage.getRecords()));
        return dtoPage;
    }
}