package cn.wbw.yyhis.controller;

import cn.wbw.yyhis.converter.B0616Converter;
import cn.wbw.yyhis.model.dto.B0616DTO;
import cn.wbw.yyhis.model.dto.B0616UpsertDTO;
import cn.wbw.yyhis.model.entity.B0616;
import cn.wbw.yyhis.service.B0616Service;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.RequiredArgsConstructor;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 手术记录管理接口
 *
 * 提供患者手术记录的管理功能，包括手术过程的详细记录管理。
 * 手术记录是手术医疗活动的核心文档，详细记录了手术的全过程，
 * 包括术前诊断、术后诊断、手术步骤、麻醉情况等关键信息，
 * 是医疗质量控制、医疗安全管理和法律保护的重要依据。
 *
 * <AUTHOR>
 * @since 2024-07-30
 */
@RestController
@RequestMapping("/b0616")
@RequiredArgsConstructor
public class B0616Controller {

    private final B0616Service b0616Service;
    private final B0616Converter b0616Converter = B0616Converter.INSTANCE;

    /**
     * 保存或更新手术记录
     *
     * 根据是否提供记录流水号来判断是新增还是更新操作。
     * 手术记录是手术医疗活动的核心文档，详细记录了手术的全过程，
     * 包括手术诊断、手术步骤、麻醉情况等关键信息。
     *
     * @param dto 手术记录数据传输对象，包含以下主要字段：
     *            - recordSn: 病程记录流水号（更新时必填，新增时可为空）
     *            - visitSn: 单次就诊唯一标识号（必填）
     *            - preOperativeDiagnosis: 术前诊断，手术前确定的诊断
     *            - postOperationDiagnosis: 术后诊断，手术后确定的诊断
     *            - surgeryName: 手术名称，具体进行的手术
     *            - anesthesiaMethod: 麻醉方式，采用的麻醉方法
     *            - surgeryStarttime: 手术开始时间
     *            - surgeryEndtime: 手术结束时间
     *            - surgeryProcess: 手术经过，详细的手术过程记录
     * @return B0616DTO 保存后的手术记录完整信息
     */
    @PostMapping("/save")
    public B0616DTO save(@RequestBody B0616UpsertDTO dto) {
        if (!StringUtils.hasText(dto.getRecordSn())) {
            B0616 newB0616 = b0616Service.addSurgeryRecord(dto);
            return b0616Converter.toDto(newB0616);
        } else {
            b0616Service.updateSurgeryRecord(dto);
            return b0616Converter.toDto(b0616Service.getById(dto.getRecordSn()));
        }
    }

    /**
     * 根据就诊流水号删除手术记录
     *
     * 删除指定就诊的手术记录。删除手术记录需要极其谨慎，
     * 因为手术记录是重要的医疗法律文档，记录了手术的详细过程，
     * 是医疗质量控制和法律保护的重要依据，
     * 建议在删除前确认该记录确实需要删除且已有备份。
     *
     * @param visitSn 单次就诊唯一标识号
     * @return Boolean 删除操作结果，true表示删除成功，false表示删除失败
     */
    @DeleteMapping("/delete/{visitSn}")
    public Boolean delete(@PathVariable String visitSn) {
        return b0616Service.deleteByVisitSn(visitSn);
    }

    /**
     * 根据就诊流水号查询手术记录详情
     *
     * 查询指定就诊的手术记录详细信息，包含完整的手术过程记录。
     *
     * @param visitSn 单次就诊唯一标识号
     * @return B0616DTO 手术记录详细信息，包含：
     *         - recordSn: 病程记录流水号
     *         - visitSn: 就诊流水号
     *         - preOperativeDiagnosis: 术前诊断
     *         - postOperationDiagnosis: 术后诊断
     *         - surgeryName: 手术名称
     *         - anesthesiaMethod: 麻醉方式
     *         - surgeryStarttime: 手术开始时间
     *         - surgeryEndtime: 手术结束时间
     *         - surgeryProcess: 手术经过详情
     *         - anesthesiaDoctorName: 麻醉医生姓名
     *         - surgeryDoctorName: 手术医生姓名
     *         - bleedingVolum: 出血量
     *         - signatureDoctor: 签名医生
     *         - recordDatetime: 记录创建时间
     *         如果该就诊没有手术记录，返回null
     */
    @GetMapping("/detail/{visitSn}")
    public B0616DTO detail(@PathVariable String visitSn) {
        return b0616Converter.toDto(b0616Service.lambdaQuery().eq(B0616::getVisitSn, visitSn).one());
    }

    /**
     * 查询所有手术记录
     *
     * 获取系统中的所有手术记录，用于统计分析或管理查看。
     * 该接口返回完整的手术记录列表，包含所有患者的手术信息。
     *
     * @return List<B0616DTO> 所有手术记录列表，包含：
     *         - recordSn: 病程记录流水号
     *         - visitSn: 就诊流水号
     *         - surgeryName: 手术名称
     *         - preOperativeDiagnosis: 术前诊断
     *         - postOperationDiagnosis: 术后诊断
     *         - anesthesiaMethod: 麻醉方式
     *         - surgeryStarttime: 手术开始时间
     *         - surgeryEndtime: 手术结束时间
     *         - surgeryDoctorName: 手术医生
     *         - recordDatetime: 记录创建时间
     */
    @GetMapping("/all")
    public List<B0616DTO> findAll() {
        List<B0616> list = b0616Service.list();
        return list.stream().map(b0616Converter::toDto).toList();
    }

    /**
     * 分页查询手术记录
     *
     * 分页获取手术记录列表，支持大数据量的手术记录查询和展示。
     * 适用于手术记录的管理界面和统计分析。
     *
     * @param pageNum 页码，从1开始，默认值为1
     * @param pageSize 每页记录数，默认值为10
     * @return IPage<B0616DTO> 分页的手术记录列表，包含：
     *         - current: 当前页码
     *         - size: 每页大小
     *         - total: 总记录数
     *         - records: 当前页的手术记录列表，每条记录包含完整的手术信息
     */
    @GetMapping("/page")
    public IPage<B0616DTO> findPage(@RequestParam(defaultValue = "1") Integer pageNum,
                                               @RequestParam(defaultValue = "10") Integer pageSize) {
        Page<B0616> page = new Page<>(pageNum, pageSize);
        Page<B0616> b0616Page = b0616Service.page(page);
        IPage<B0616DTO> dtoPage = b0616Page.convert(b0616Converter::toDto);
        return dtoPage;
    }
} 