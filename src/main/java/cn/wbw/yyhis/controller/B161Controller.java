package cn.wbw.yyhis.controller;

import org.springframework.web.bind.annotation.*;
import org.springframework.beans.factory.annotation.Autowired;
import cn.wbw.yyhis.model.dto.B161DTO;
import cn.wbw.yyhis.model.entity.B161;
import cn.wbw.yyhis.service.B161Service;
import cn.wbw.yyhis.converter.B161Converter;

import java.util.List;

/**
 * 常规检查记录管理接口
 *
 * 提供患者常规检查记录的管理功能，包括影像学检查、超声检查、内镜检查等
 * 各类常规检查项目的记录管理。常规检查是临床诊断的重要依据，
 * 为医生提供患者病情的客观影像学和检查数据支持。
 *
 * <AUTHOR>
 * @since 2024-07-26
 */
@RestController
@RequestMapping("/b161")
public class B161Controller {

    @Autowired
    private B161Service b161Service;

    @Autowired
    private B161Converter b161Converter;

    /**
     * 更新常规检查记录
     *
     * 更新指定的常规检查记录信息，可以修改检查结果、诊断结论、
     * 检查描述等字段。检查记录的更新需要严格的权限控制，
     * 确保检查结果的准确性和可追溯性。
     *
     * @param b161DTO 常规检查记录数据传输对象，包含以下主要字段：
     *                - visitSn: 单次就诊唯一标识号（必填）
     *                - reportNo: 报告单号，检查记录的唯一标识
     *                - examItemType: 检查项目类型（如：CT、MRI、超声等）
     *                - examSites: 检查部位（如：胸部、腹部、头部等）
     *                - examItemName: 检查项目名称
     *                - examDiagConclusion: 检查诊断结论
     *                - examDiagDescription: 检查诊断描述
     *                - recordDatetime: 记录创建时间
     *                - examDatetime: 检查时间
     *                - applyDatetime: 申请时间
     * @return Boolean 更新操作结果，true表示更新成功，false表示更新失败
     */
    @PutMapping("/update")
    public Boolean update(@RequestBody B161DTO b161DTO) {
        return b161Service.updateDto(b161DTO);
    }

    /**
     * 根据报告单号删除常规检查记录
     *
     * 删除指定的常规检查记录。删除检查记录需要谨慎操作，
     * 建议在删除前确认该记录不再需要或已有备份，
     * 因为检查记录是重要的医疗诊断依据。
     *
     * @param id 报告单号，用于唯一标识一条检查记录
     * @return Boolean 删除操作结果，true表示删除成功，false表示删除失败
     */
    @DeleteMapping("/delete/{id}")
    public Boolean delete(@PathVariable("id") String id) {
        return b161Service.deleteByReportNo(id);
    }

    /**
     * 根据就诊流水号查询常规检查记录列表
     *
     * 查询指定患者就诊期间的所有常规检查记录，包括各种影像学检查、
     * 超声检查、内镜检查等。检查记录按时间顺序展示，
     * 为医生提供患者的完整检查历史。
     *
     * @param visitSn 单次就诊唯一标识号，用于筛选特定就诊的检查记录
     * @return List<B161DTO> 常规检查记录列表，包含该就诊的所有检查记录：
     *         - reportNo: 报告单号
     *         - examItemType: 检查项目类型
     *         - examSites: 检查部位
     *         - examItemName: 检查项目名称
     *         - examDiagConclusion: 检查诊断结论
     *         - examDiagDescription: 检查诊断描述
     *         - examDatetime: 检查时间
     *         - applyDatetime: 申请时间
     *         - recordDatetime: 记录时间
     *         如果该就诊没有检查记录，返回空列表
     */
    @GetMapping("/list/{visitSn}")
    public List<B161DTO> list(@PathVariable String visitSn) {
        List<B161> list = b161Service.lambdaQuery().eq(B161::getVisitSn,visitSn).list();
        return list.stream().map(b161Converter::entityToDto).toList();
    }
}