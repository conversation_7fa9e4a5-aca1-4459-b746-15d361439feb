package cn.wbw.yyhis.controller;

import cn.wbw.yyhis.converter.B0617Converter;
import cn.wbw.yyhis.model.dto.B0617DTO;
import cn.wbw.yyhis.model.dto.B0617UpsertDTO;
import cn.wbw.yyhis.model.entity.B0617;
import cn.wbw.yyhis.service.B0617Service;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.RequiredArgsConstructor;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 术后首次病程记录管理接口
 *
 * 提供患者术后首次病程记录的管理功能，包括手术后第一份病程记录的管理。
 * 术后首次病程记录是手术后医疗管理的重要文档，详细记录了手术后患者的
 * 恢复情况、术后处理措施、注意事项等关键信息，
 * 为术后康复治疗和医疗安全管理提供重要依据。
 *
 * <AUTHOR>
 * @since 2024-07-30
 */
@RestController
@RequestMapping("/b0617")
@RequiredArgsConstructor
public class B0617Controller {

    private final B0617Service b0617Service;
    private final B0617Converter b0617Converter = B0617Converter.INSTANCE;

    /**
     * 保存或更新术后首次病程记录
     *
     * 根据是否提供记录流水号来判断是新增还是更新操作。
     * 术后首次病程记录是手术后医疗管理的重要文档，详细记录了
     * 手术后患者的恢复情况和后续治疗计划。
     *
     * @param dto 术后首次病程记录数据传输对象，包含以下主要字段：
     *            - recordSn: 病程记录流水号（更新时必填，新增时可为空）
     *            - visitSn: 单次就诊唯一标识号（必填）
     *            - postOperationDiagnosis: 术后诊断，手术后确定的诊断
     *            - surgeryName: 手术名称，已完成的手术
     *            - anesthesiaMethod: 麻醉方式，采用的麻醉方法
     *            - surgeryStartDatetime: 手术开始时间
     *            - surgeryEndDatetime: 手术结束时间
     *            - surgeryProcess: 术后情况/手术经过，手术过程和术后状况
     *            - postoperationTreatment: 术后处理措施，术后的治疗方案
     *            - mattersNeedCaution: 术后注意事项，需要特别关注的问题
     * @return B0617DTO 保存后的术后首次病程记录完整信息
     */
    @PostMapping("/save")
    public B0617DTO save(@RequestBody B0617UpsertDTO dto) {
        if (!StringUtils.hasText(dto.getRecordSn())) {
            B0617 newB0617 = b0617Service.addPostoperativeCourse(dto);
            return b0617Converter.toDto(newB0617);
        } else {
            b0617Service.updatePostoperativeCourse(dto);
            return b0617Converter.toDto(b0617Service.getById(dto.getRecordSn()));
        }
    }

    /**
     * 根据就诊流水号删除术后首次病程记录
     *
     * 删除指定就诊的术后首次病程记录。删除术后病程记录需要谨慎操作，
     * 因为这是术后医疗管理的重要文档，记录了患者术后的恢复情况，
     * 建议在删除前确认该记录确实需要删除且已有备份。
     *
     * @param visitSn 单次就诊唯一标识号
     * @return Boolean 删除操作结果，true表示删除成功，false表示删除失败
     */
    @DeleteMapping("/delete/{visitSn}")
    public Boolean delete(@PathVariable String visitSn) {
        return b0617Service.deleteByVisitSn(visitSn);
    }

    /**
     * 根据就诊流水号查询术后首次病程记录详情
     *
     * 查询指定就诊的术后首次病程记录详细信息，包含完整的术后恢复记录。
     *
     * @param visitSn 单次就诊唯一标识号
     * @return B0617DTO 术后首次病程记录详细信息，包含：
     *         - recordSn: 病程记录流水号
     *         - visitSn: 就诊流水号
     *         - postOperationDiagnosis: 术后诊断
     *         - surgeryName: 手术名称
     *         - anesthesiaMethod: 麻醉方式
     *         - surgeryStartDatetime: 手术开始时间
     *         - surgeryEndDatetime: 手术结束时间
     *         - surgeryProcess: 术后情况描述
     *         - postoperationTreatment: 术后处理措施
     *         - mattersNeedCaution: 术后注意事项
     *         - signatureDoctor: 签名医生
     *         - recordDatetime: 记录创建时间
     *         如果该就诊没有术后首次病程记录，返回null
     */
    @GetMapping("/detail/{visitSn}")
    public B0617DTO detail(@PathVariable String visitSn) {
        return b0617Converter.toDto(b0617Service.lambdaQuery().eq(B0617::getVisitSn, visitSn).one());
    }

    /**
     * 查询所有术后首次病程记录
     *
     * 获取系统中的所有术后首次病程记录，用于统计分析或管理查看。
     * 该接口返回完整的术后病程记录列表，包含所有患者的术后恢复信息。
     *
     * @return List<B0617DTO> 所有术后首次病程记录列表，包含：
     *         - recordSn: 病程记录流水号
     *         - visitSn: 就诊流水号
     *         - postOperationDiagnosis: 术后诊断
     *         - surgeryName: 手术名称
     *         - surgeryStartDatetime: 手术开始时间
     *         - surgeryEndDatetime: 手术结束时间
     *         - postoperationTreatment: 术后处理措施
     *         - mattersNeedCaution: 术后注意事项
     *         - recordDatetime: 记录创建时间
     */
    @GetMapping("/all")
    public List<B0617DTO> findAll() {
        List<B0617> list = b0617Service.list();
        return list.stream().map(b0617Converter::toDto).toList();
    }

    /**
     * 分页查询术后首次病程记录
     *
     * 分页获取术后首次病程记录列表，支持大数据量的病程记录查询和展示。
     * 适用于术后病程记录的管理界面和统计分析。
     *
     * @param pageNum 页码，从1开始，默认值为1
     * @param pageSize 每页记录数，默认值为10
     * @return IPage<B0617DTO> 分页的术后首次病程记录列表，包含：
     *         - current: 当前页码
     *         - size: 每页大小
     *         - total: 总记录数
     *         - records: 当前页的术后病程记录列表，每条记录包含完整的术后恢复信息
     */
    @GetMapping("/page")
    public IPage<B0617DTO> findPage(@RequestParam(defaultValue = "1") Integer pageNum,
                                               @RequestParam(defaultValue = "10") Integer pageSize) {
        Page<B0617> page = new Page<>(pageNum, pageSize);
        Page<B0617> b0617Page = b0617Service.page(page);
        IPage<B0617DTO> dtoPage = b0617Page.convert(b0617Converter::toDto);
        return dtoPage;
    }
} 