package cn.wbw.yyhis.controller;

import cn.wbw.yyhis.converter.B0613Converter;
import cn.wbw.yyhis.model.dto.B0613DTO;
import cn.wbw.yyhis.model.dto.B0613UpsertDTO;
import cn.wbw.yyhis.model.entity.B0613;
import cn.wbw.yyhis.service.B0613Service;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.RequiredArgsConstructor;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 术前讨论记录管理接口
 *
 * 提供患者术前讨论记录的管理功能，包括手术前多学科团队讨论的记录管理。
 * 术前讨论是手术安全的重要保障，通过多学科专家的集体讨论，
 * 确定手术方案、评估手术风险、制定应急预案，为手术的顺利进行提供专业支持。
 *
 * <AUTHOR>
 * @since 2024-07-30
 */
@RestController
@RequestMapping("/b0613")
@RequiredArgsConstructor
public class B0613Controller {

    private final B0613Service b0613Service;
    private final B0613Converter converter = B0613Converter.INSTANCE;

    /**
     * 保存或更新术前讨论记录
     *
     * 根据是否提供记录流水号来判断是新增还是更新操作。
     * 术前讨论记录是手术安全管理的重要文档，记录了多学科团队
     * 对手术方案的讨论过程和决策结果。
     *
     * @param dto 术前讨论记录数据传输对象，包含以下主要字段：
     *            - recordSn: 病程记录流水号（更新时必填，新增时可为空）
     *            - visitSn: 单次就诊唯一标识号（必填）
     *            - discussionDatetime: 讨论日期，术前讨论的具体时间
     *            - participantsList: 参加人员，参与讨论的医护人员名单
     *            - preoperationPreparation: 术前准备，手术前的各项准备工作
     *            - operationIndication: 手术指征，进行手术的医学依据
     *            - surgeryMethod: 手术方式，拟采用的手术方法
     *            - surgeryPositions: 手术体位，患者在手术中的体位
     *            - operationSetps: 手术步骤，详细的手术操作流程
     *            - possibleProblemsAndSolution: 术中注意事项，可能遇到的问题及解决方案
     *            - discussionOpinion: 讨论意见，各专家的意见和建议
     *            - hostConclusion: 讨论小结，讨论的最终结论
     * @return B0613DTO 保存后的术前讨论记录完整信息
     */
    @PostMapping("/save")
    public B0613DTO save(@RequestBody B0613UpsertDTO dto) {
        if (!StringUtils.hasText(dto.getRecordSn())) {
            B0613 newB0613 = b0613Service.addPreoperativeDiscussion(dto);
            return converter.entityToDto(newB0613);
        } else {
            b0613Service.updatePreoperativeDiscussion(dto);
            return converter.entityToDto(b0613Service.getById(dto.getRecordSn()));
        }
    }

    /**
     * 根据就诊流水号删除术前讨论记录
     *
     * 删除指定就诊的术前讨论记录。删除术前讨论记录需要谨慎操作，
     * 因为这是手术安全管理的重要文档，记录了手术决策过程，
     * 建议在删除前确认该记录确实需要删除且已有备份。
     *
     * @param visitSn 单次就诊唯一标识号
     * @return Boolean 删除操作结果，true表示删除成功，false表示删除失败
     */
    @DeleteMapping("/delete/{visitSn}")
    public Boolean delete(@PathVariable String visitSn) {
        return b0613Service.deleteByVisitSn(visitSn);
    }

    /**
     * 查询所有术前讨论记录
     *
     * 获取系统中的所有术前讨论记录，用于统计分析或管理查看。
     * 该接口返回完整的术前讨论记录列表，包含所有患者的讨论信息。
     *
     * @return List<B0613DTO> 所有术前讨论记录列表，包含：
     *         - recordSn: 病程记录流水号
     *         - visitSn: 就诊流水号
     *         - discussionDatetime: 讨论时间
     *         - participantsList: 参与人员
     *         - operationIndication: 手术指征
     *         - surgeryMethod: 手术方式
     *         - discussionOpinion: 讨论意见
     *         - hostConclusion: 讨论结论
     *         - recordDatetime: 记录创建时间
     */
    @GetMapping("/all")
    public List<B0613DTO> findAll() {
        return converter.entityListToDtoList(b0613Service.list());
    }

    /**
     * 根据就诊流水号查询术前讨论记录详情
     *
     * 查询指定就诊的术前讨论记录详细信息，包含完整的讨论过程记录。
     *
     * @param visitSn 单次就诊唯一标识号
     * @return B0613DTO 术前讨论记录详细信息，包含：
     *         - recordSn: 病程记录流水号
     *         - visitSn: 就诊流水号
     *         - discussionDatetime: 讨论日期
     *         - participantsList: 参加人员名单
     *         - preoperationPreparation: 术前准备事项
     *         - operationIndication: 手术指征
     *         - surgeryMethod: 手术方式
     *         - surgeryPositions: 手术体位
     *         - operationSetps: 手术步骤
     *         - possibleProblemsAndSolution: 术中注意事项
     *         - discussionOpinion: 专家讨论意见
     *         - hostConclusion: 讨论小结
     *         - signatureDoctor: 签名医生
     *         - recordDatetime: 记录创建时间
     *         如果该就诊没有术前讨论记录，返回null
     */
    @GetMapping("/detail/{visitSn}")
    public B0613DTO detail(@PathVariable String visitSn) {
        return converter.entityToDto(b0613Service.lambdaQuery().eq(B0613::getVisitSn, visitSn).one());
    }

    /**
     * 分页查询术前讨论记录
     *
     * 分页获取术前讨论记录列表，支持大数据量的讨论记录查询和展示。
     * 适用于术前讨论记录的管理界面和统计分析。
     *
     * @param pageNum 页码，从1开始，默认值为1
     * @param pageSize 每页记录数，默认值为10
     * @return Page<B0613DTO> 分页的术前讨论记录列表，包含：
     *         - current: 当前页码
     *         - size: 每页大小
     *         - total: 总记录数
     *         - records: 当前页的术前讨论记录列表，每条记录包含完整的讨论信息
     */
    @GetMapping("/page")
    public Page<B0613DTO> findPage(@RequestParam(defaultValue = "1") Integer pageNum,
                                   @RequestParam(defaultValue = "10") Integer pageSize) {
        Page<B0613> page = new Page<>(pageNum, pageSize);
        Page<B0613> entityPage = b0613Service.page(page);
        Page<B0613DTO> dtoPage = new Page<>(entityPage.getCurrent(), entityPage.getSize(), entityPage.getTotal());
        dtoPage.setRecords(converter.entityListToDtoList(entityPage.getRecords()));
        return dtoPage;
    }
} 